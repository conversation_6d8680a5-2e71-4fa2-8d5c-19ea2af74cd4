# Momentum Logic Debugging Summary

we were running 

-------
python main_program.py --assets ETH/USDT BTC/USDT SOL/USDT SUI/USDT XRP/USDT --trend-method "PGO For Loop" --timeframe 1d --analysis-start-date 2023-10-19 --analysis-end-date 2024-01-09--n-assets 1 --transaction-fee 0.001 --mtpi-combination-method consensus --mtpi-long-threshold 0.1 --mtpi-short-threshold -0.1 --mtpi-indicators pgo bollinger_bands dwma_score dema_super_score dpsd_score aad_score dynamic_ema_score quantile_dema_score --ratio-calculation independent --tie-breaking-strategy momentum
------


## Problem Statement
The asset selection system is not properly applying momentum-based tie-breaking when assets have equal scores. Instead of selecting the asset with the best momentum (highest score delta), it appears to be using simple sorting or incorrect momentum calculations.

## Key Violation Example
**Date: 2024-01-06**
- **SOL/USDT**: 4.0 → 3.0 (Δ=-1.0) - DECLINING momentum
- **SUI/USDT**: 3.0 → 3.0 (Δ=+0.0) - FLAT momentum
- **Expected**: SUI/USDT should be selected (better momentum)
- **Actual**: SOL/USDT was selected ❌

## Investigation Timeline

### 1. Initial Discovery
- Used `analyze_momentum_logic.py` to detect 4 momentum violations in CSV files
- Violations found at: 2024-01-06, 2024-02-29, 2024-07-25, 2025-06-17
- All violations showed assets with negative momentum being selected over assets with flat/positive momentum

### 2. First Hypothesis: Previous Scores Retrieval Issue
- **Problem**: Suspected mismatch between raw scores and filtered scores
- **Investigation**: Created debug scripts (`debug_previous_scores.py`, `debug_index_mapping.py`)
- **Finding**: Previous scores retrieval was working correctly
- **Status**: ❌ Not the root cause

### 3. Second Hypothesis: Filtering Logic Mismatch
- **Problem**: Current scores used filtered logic, previous scores used raw logic
- **Fix Applied**: Modified `main_program.py` lines 979-1002 to apply same filtering to previous scores
- **Result**: Issue persisted
- **Status**: ❌ Partial fix, but not complete solution

### 4. Third Hypothesis: Momentum Function Not Being Called
- **Investigation**: Added debug logging with 🚨 emojis to track function calls
- **Finding**: Momentum function IS being called
- **Evidence**: 
  ```
  🚨 [MOMENTUM] FUNCTION CALLED! find_top_n_assets_with_momentum for n=1
  🚨 [MOMENTUM] Current scores: {'SOL/USDT': 3.0, 'SUI/USDT': 3.0, ...}
  🚨 [MOMENTUM] Previous scores: {'SOL/USDT': 3.0, 'SUI/USDT': 3.0, ...}
  ```
- **Status**: ✅ Function is called, but with wrong data

### 5. Current Discovery: Wrong Previous Scores
- **Critical Finding**: Previous scores being passed to momentum function are incorrect
- **Expected Previous Scores**: `{'SOL/USDT': 4.0, 'SUI/USDT': 3.0}`
- **Actual Previous Scores**: `{'SOL/USDT': 3.0, 'SUI/USDT': 3.0}`
- **Impact**: SOL/USDT score is wrong (3.0 instead of 4.0), making momentum calculation incorrect

## Current Status

### What Works
- ✅ Momentum function is being called
- ✅ Tie-breaking strategy parameter is correctly set to 'momentum'
- ✅ Code path reaches the momentum logic
- ✅ Filtering logic for current scores works correctly

### What's Broken
- ❌ Previous scores retrieved from `daily_scores_df` are incorrect
- ❌ SOL/USDT previous score shows as 3.0 instead of expected 4.0
- ❌ This causes momentum calculation to be wrong (Δ=0.0 instead of Δ=-1.0)

### Root Cause Analysis

Looking at the code, I can see the issue now. The problem is in the **data retrieval logic** in `main_program.py` around lines 987-1002.

**The Issue**:
- Line 987: `prev_score_date = self.daily_scores_df.index[i-1]`
- This gets the date from the DataFrame index at position `i-1`
- But the DataFrame index might not contain the expected dates due to filtering or data gaps

**What's happening**:
1. The code assumes `daily_scores_df.index[i-1]` gives us the previous day's date
2. But if there are missing dates or filtering applied, `index[i-1]` might not be the actual previous day
3. This causes wrong previous scores to be retrieved

**Simple Fix Needed**:
Instead of using index position `i-1`, we should:
1. Calculate the actual previous date: `prev_date = date - timedelta(days=1)`
2. Look up scores for that specific date in the DataFrame
3. Handle missing dates gracefully

### Next Investigation Steps
1. **Verify Index Issue**: Check if `daily_scores_df.index[i-1]` matches the expected previous date
2. **Fix Date Logic**: Replace index-based lookup with actual date calculation
3. **Test Fix**: Run the momentum approach with corrected date logic

## Technical Details

### Files Modified
- `main_program.py`: Lines 979-1002 (previous scores filtering)
- `main_program.py`: Lines 1037-1046 (momentum selection logic)
- `src/momentum_scoring.py`: Lines 105-127 (debug logging)

### Debug Logging Added
- 🚨 markers for critical debug points
- Function call tracking in momentum_scoring.py
- Score comparison logging in main_program.py
- Asset selection path verification

### Test Configuration
```bash
python main_program.py --assets ETH/USDT BTC/USDT SOL/USDT SUI/USDT XRP/USDT \
  --trend-method "PGO For Loop" --timeframe 1d \
  --analysis-start-date 2024-01-05 --analysis-end-date 2024-01-08 \
  --n-assets 1 --tie-breaking-strategy momentum
```

## Key Observations
1. **Behavior Differs by Start Date**: Short-term tests (2024-01-05) vs long-term tests (2023-10-19) show different behavior
2. **Consistent Violations**: Same violation pattern appears in both short and long backtests
3. **Data Corruption**: Previous scores are being corrupted somewhere in the data pipeline
4. **Momentum Logic Works**: The momentum selection algorithm itself is correct, but receives wrong input data
