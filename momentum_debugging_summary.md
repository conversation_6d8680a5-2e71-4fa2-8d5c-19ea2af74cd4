# Momentum Logic Debugging Summary

## Problem Statement
The asset selection system is not properly applying momentum-based tie-breaking when assets have equal scores. Instead of selecting the asset with the best momentum (highest score delta), it appears to be using simple sorting or incorrect momentum calculations.

## Key Violation Example
**Date: 2024-01-06**
- **SOL/USDT**: 4.0 → 3.0 (Δ=-1.0) - DECLINING momentum
- **SUI/USDT**: 3.0 → 3.0 (Δ=+0.0) - FLAT momentum
- **Expected**: SUI/USDT should be selected (better momentum)
- **Actual**: SOL/USDT was selected ❌

## Investigation Timeline

### 1. Initial Discovery
- Used `analyze_momentum_logic.py` to detect 4 momentum violations in CSV files
- Violations found at: 2024-01-06, 2024-02-29, 2024-07-25, 2025-06-17
- All violations showed assets with negative momentum being selected over assets with flat/positive momentum

### 2. First Hypothesis: Previous Scores Retrieval Issue
- **Problem**: Suspected mismatch between raw scores and filtered scores
- **Investigation**: Created debug scripts (`debug_previous_scores.py`, `debug_index_mapping.py`)
- **Finding**: Previous scores retrieval was working correctly
- **Status**: ❌ Not the root cause

### 3. Second Hypothesis: Filtering Logic Mismatch
- **Problem**: Current scores used filtered logic, previous scores used raw logic
- **Fix Applied**: Modified `main_program.py` lines 979-1002 to apply same filtering to previous scores
- **Result**: Issue persisted
- **Status**: ❌ Partial fix, but not complete solution

### 4. Third Hypothesis: Momentum Function Not Being Called
- **Investigation**: Added debug logging with 🚨 emojis to track function calls
- **Finding**: Momentum function IS being called
- **Evidence**: 
  ```
  🚨 [MOMENTUM] FUNCTION CALLED! find_top_n_assets_with_momentum for n=1
  🚨 [MOMENTUM] Current scores: {'SOL/USDT': 3.0, 'SUI/USDT': 3.0, ...}
  🚨 [MOMENTUM] Previous scores: {'SOL/USDT': 3.0, 'SUI/USDT': 3.0, ...}
  ```
- **Status**: ✅ Function is called, but with wrong data

### 5. Current Discovery: Wrong Previous Scores
- **Critical Finding**: Previous scores being passed to momentum function are incorrect
- **Expected Previous Scores**: `{'SOL/USDT': 4.0, 'SUI/USDT': 3.0}`
- **Actual Previous Scores**: `{'SOL/USDT': 3.0, 'SUI/USDT': 3.0}`
- **Impact**: SOL/USDT score is wrong (3.0 instead of 4.0), making momentum calculation incorrect

## Current Status

### What Works
- ✅ Momentum function is being called
- ✅ Tie-breaking strategy parameter is correctly set to 'momentum'
- ✅ Code path reaches the momentum logic
- ✅ Filtering logic for current scores works correctly

### What's Broken
- ❌ Previous scores retrieved from `daily_scores_df` are incorrect
- ❌ SOL/USDT previous score shows as 3.0 instead of expected 4.0
- ❌ This causes momentum calculation to be wrong (Δ=0.0 instead of Δ=-1.0)

### Next Investigation Steps
1. **Check Raw Data**: Examine what's actually stored in `daily_scores_df` at the previous date
2. **Verify Data Source**: Confirm if the issue is in score calculation or data storage
3. **Compare Dates**: Check if there's a date indexing issue causing wrong data retrieval

## Technical Details

### Files Modified
- `main_program.py`: Lines 979-1002 (previous scores filtering)
- `main_program.py`: Lines 1037-1046 (momentum selection logic)
- `src/momentum_scoring.py`: Lines 105-127 (debug logging)

### Debug Logging Added
- 🚨 markers for critical debug points
- Function call tracking in momentum_scoring.py
- Score comparison logging in main_program.py
- Asset selection path verification

### Test Configuration
```bash
python main_program.py --assets ETH/USDT BTC/USDT SOL/USDT SUI/USDT XRP/USDT \
  --trend-method "PGO For Loop" --timeframe 1d \
  --analysis-start-date 2024-01-05 --analysis-end-date 2024-01-08 \
  --n-assets 1 --tie-breaking-strategy momentum
```

## Key Observations
1. **Behavior Differs by Start Date**: Short-term tests (2024-01-05) vs long-term tests (2023-10-19) show different behavior
2. **Consistent Violations**: Same violation pattern appears in both short and long backtests
3. **Data Corruption**: Previous scores are being corrupted somewhere in the data pipeline
4. **Momentum Logic Works**: The momentum selection algorithm itself is correct, but receives wrong input data
