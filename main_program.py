#!/usr/bin/env python
"""
Main program for the Asset Rotation Strategy.
This script runs the strategy with comprehensive logging to verify:
1. Top N assets are correctly selected
2. Allocation percentages are correct
3. Returns are calculated correctly
4. Transaction costs are applied properly
"""

import os
import sys
import logging
import pandas as pd
import time
from datetime import datetime, timedelta

# Set Matplotlib to use non-interactive backend to prevent warnings when running in background threads
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from typing import List, Optional, Dict, Any
import argparse
import matplotlib.gridspec as gridspec

# Set pandas option to opt into future behavior for ffill/bfill operations
pd.set_option('future.no_silent_downcasting', True)

# Add the parent directory to the path so we can import from src
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import necessary modules
from src.data_fetcher import fetch_ohlcv_data
from src.strategy import calculate_daily_scores
from src.MTPI_signal_handler import fetch_historical_mtpi_signals
from src.config_manager import load_config
from src.performance import calculate_all_metrics
from src.performance_tracker import save_daily_metrics, ensure_metrics_directory

# Try to import memecoin data fetchers
try:
    from src.geckoterminal_fetcher import fetch_geckoterminal_data
    GECKOTERMINAL_AVAILABLE = True
except ImportError:
    GECKOTERMINAL_AVAILABLE = False
    logging.warning("GeckoTerminal fetcher not available. Historical memecoin fetching will be disabled.")

# Import from src module
from src.scoring_n import find_top_n_assets_for_day

# Configure logging - using INFO level for better debugging
# This will show more detailed logs to help diagnose issues
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('allocation_test.log', mode='w')
    ]
)

# Constants
RSI_TV_SMA_SIGNAL_COL = 'rsi_tv_sma_signal'

def format_metric(value: Optional[float], is_percent: bool = False, decimals: int = 2) -> str:
    """Formats a metric value for display in tables."""
    if value is None:
        return "N/A"
    if is_percent:
        # For percentage values, don't multiply by 100 again if they're already in percentage form
        return f"{value:.{decimals}f}%"
    else:
        return f"{value:.{decimals}f}"


def format_elapsed_time(seconds):
    """Format seconds into a human-readable string (e.g., '2m 34s')"""
    minutes, seconds = divmod(int(seconds), 60)
    hours, minutes = divmod(minutes, 60)

    if hours > 0:
        return f"{hours}h {minutes}m {seconds}s"
    elif minutes > 0:
        return f"{minutes}m {seconds}s"
    else:
        return f"{seconds}s"


class AllocationTester:
    """Class to test allocation logic in the top_n strategy."""

    def __init__(self,
                 timeframe: str = '4h',
                 mtpi_timeframe: str = '1d',
                 analysis_start_date: str = '2023-02-15',
                 analysis_end_date: Optional[str] = None,
                 n_assets: int = 2,
                 transaction_fee_rate: float = 0.001,
                 selected_assets: Optional[List[str]] = None,
                 use_cache: bool = True,
                 initial_capital: float = 1.0,
                 wait_for_confirmed_signals: bool = True,
                 use_mtpi: bool = True,
                 use_weighted_allocation: bool = False,
                 weights: Optional[List[float]] = None,
                 enable_rebalancing: bool = False,
                 rebalance_threshold: float = 0.05,
                 geckoterminal_tokens: Optional[List[Dict[str, str]]] = None,
                 trend_method: Optional[str] = None,
                 pgo_length: int = 35,
                 pgo_upper_threshold: float = 1.1,
                 pgo_lower_threshold: float = -0.58,
                 context: str = 'backtesting',
                 execution_timing: str = 'candle_close',  # New parameter: 'candle_close' or 'manual_12pm'
                 ratio_calculation: str = 'manual_inversion',  # New parameter: 'manual_inversion' or 'independent'
                 tie_breaking_strategy: str = 'momentum',  # New parameter: 'incumbent' or 'momentum'
                 # MTPI indicator selection parameters
                 mtpi_indicators: Optional[List[str]] = None,
                 mtpi_combination_method: Optional[str] = None,
                 mtpi_long_threshold: Optional[float] = None,
                 mtpi_short_threshold: Optional[float] = None,
                 config_path: Optional[str] = None):
        """Initialize the tester with strategy parameters."""
        self.timeframe = timeframe
        self.mtpi_timeframe = mtpi_timeframe
        self.analysis_start_date = analysis_start_date
        self.analysis_end_date = analysis_end_date
        self.n_assets = n_assets
        self.transaction_fee_rate = transaction_fee_rate
        # Handle default assets - only use defaults if no assets are provided at all
        if selected_assets is None:
            self.selected_assets = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'SUI/USDT']
        else:
            self.selected_assets = selected_assets
        self.use_cache = use_cache
        self.force_refresh = False  # Default to False, will be set from command line args
        self.initial_capital = initial_capital
        self.wait_for_confirmed_signals = wait_for_confirmed_signals
        self.use_mtpi = use_mtpi
        self.use_weighted_allocation = use_weighted_allocation
        self.enable_rebalancing = enable_rebalancing
        self.rebalance_threshold = rebalance_threshold

        # Set weights for weighted allocation
        if self.use_weighted_allocation and weights:
            self.weights = weights
        elif self.use_weighted_allocation:
            # Default weights: 70%, 20%, 10% for top 3 assets
            if self.n_assets == 3:
                self.weights = [0.7, 0.2, 0.1]
            # Default weights: 60%, 25%, 10%, 5% for top 4 assets
            elif self.n_assets == 4:
                self.weights = [0.6, 0.25, 0.1, 0.05]
            # Equal weights for other cases
            else:
                self.weights = [1.0 / self.n_assets] * self.n_assets
        else:
            self.weights = None

        # GeckoTerminal tokens in format [{'network': 'solana', 'token_address': '...', 'symbol': 'TOKEN/USDT'}]
        self.geckoterminal_tokens = geckoterminal_tokens or []

        # Set up weights for weighted allocation
        if use_weighted_allocation:
            if weights:
                # Normalize weights if they don't sum to 1
                if sum(weights) != 1.0:
                    total = sum(weights)
                    self.weights = [w / total for w in weights]
                    logging.warning(f"Weights sum to {total}, normalized to: {self.weights}")
                else:
                    self.weights = weights
            elif n_assets == 3:
                # Default 70-20-10 split for top 3 assets
                self.weights = [0.7, 0.2, 0.1]
                logging.info(f"Using default weights for 3 assets: {self.weights}")
            else:
                # Equal weights for other cases
                self.weights = [1.0 / n_assets] * n_assets
                logging.info(f"Using equal weights for {n_assets} assets: {self.weights}")
        else:
            self.weights = None

        # Load configuration
        self.config = load_config(config_path)
        self.exchange_id = self.config.get('exchange', 'binance')

        # Handle trend method - use parameter if provided, otherwise use config
        if trend_method is not None:
            self.trend_method = trend_method
        else:
            self.trend_method = self.config.get('settings', {}).get('trend_method', 'RSI')

        # Handle "PGO" as an alias for "PGO For Loop" for compatibility
        if self.trend_method == "PGO":
            self.trend_method = "PGO For Loop"
            logging.info(f"Converting trend method 'PGO' to 'PGO For Loop' for compatibility")

        # Store PGO parameters for asset trend detection
        self.pgo_length = pgo_length
        self.pgo_upper_threshold = pgo_upper_threshold
        self.pgo_lower_threshold = pgo_lower_threshold

        # Store context for rate limiting
        self.context = context
        logging.info(f"Execution context: {self.context}")

        # Store execution timing parameter
        self.execution_timing = execution_timing
        logging.info(f"Execution timing: {self.execution_timing}")

        # Store ratio calculation method
        self.ratio_calculation = ratio_calculation
        logging.info(f"Ratio calculation method: {self.ratio_calculation}")

        # Store tie-breaking strategy
        self.tie_breaking_strategy = tie_breaking_strategy
        logging.info(f"Tie-breaking strategy: {self.tie_breaking_strategy}")

        # Store MTPI indicator selection parameters
        self.mtpi_indicators = mtpi_indicators
        self.mtpi_combination_method = mtpi_combination_method
        self.mtpi_long_threshold = mtpi_long_threshold
        self.mtpi_short_threshold = mtpi_short_threshold

        logging.info(f"Asset trend PGO parameters: length={self.pgo_length}, upper_threshold={self.pgo_upper_threshold}, lower_threshold={self.pgo_lower_threshold}")

        if self.mtpi_indicators:
            logging.info(f"MTPI indicators override: {self.mtpi_indicators}")
        if self.mtpi_combination_method:
            logging.info(f"MTPI combination method override: {self.mtpi_combination_method}")
        if self.mtpi_long_threshold is not None:
            logging.info(f"MTPI long threshold override: {self.mtpi_long_threshold}")
        if self.mtpi_short_threshold is not None:
            logging.info(f"MTPI short threshold override: {self.mtpi_short_threshold}")

        # Initialize data structures
        self.data_dict = {}
        self.daily_scores_df = None
        self.mtpi_signals = None
        self.strategy_equity_curve = None
        self.assets_held_df = None
        self.allocation_history = []
        self.return_history = []

        # Initialize timing variables
        self.start_time = None
        self.end_time = None
        self.elapsed_time = None
        self.total_elapsed_time = None  # For tracking total execution time

        # Initialize performance metrics
        self.strategy_metrics = None
        self.bnh_metrics = {}

    def _apply_incumbent_tie_breaking_equal(self, scores, current_holdings, top_assets, mtpi_signal):
        """Apply incumbent tie-breaking logic for equal allocation."""
        if not scores or not current_holdings or not top_assets:
            return top_assets

        # Find the maximum score
        max_score = max(scores.values())
        tied_assets = [asset for asset, score in scores.items() if score == max_score]

        # If there's no tie, return the original selection
        if len(tied_assets) <= 1:
            return top_assets

        # Check if any currently held assets are among the tied assets
        current_assets = set(current_holdings.keys())
        tied_incumbents = current_assets.intersection(set(tied_assets))

        if tied_incumbents:
            # Keep the incumbent(s) that are tied for the top score
            # For single asset selection (n_assets=1), keep the first incumbent found
            if self.n_assets == 1:
                incumbent_asset = list(tied_incumbents)[0]
                logging.info(f"[TIE-BREAKING] Incumbent approach: Keeping {incumbent_asset} (tied at score {max_score})")
                return [incumbent_asset]
            else:
                # For multi-asset selection, keep incumbents and fill remaining slots
                incumbents_to_keep = list(tied_incumbents)[:self.n_assets]
                remaining_slots = self.n_assets - len(incumbents_to_keep)

                if remaining_slots > 0:
                    # Fill remaining slots with non-incumbent tied assets
                    non_incumbent_tied = [asset for asset in tied_assets if asset not in tied_incumbents]
                    additional_assets = non_incumbent_tied[:remaining_slots]
                    final_assets = incumbents_to_keep + additional_assets
                else:
                    final_assets = incumbents_to_keep

                logging.info(f"[TIE-BREAKING] Incumbent approach: Keeping incumbents {incumbents_to_keep} from tied assets {tied_assets}")
                return final_assets
        else:
            # No incumbents in tied assets, use momentum approach (dictionary order)
            from src.scoring import find_best_asset_for_day
            if self.n_assets == 1:
                best_asset = find_best_asset_for_day(scores, mtpi_signal)
                logging.info(f"[TIE-BREAKING] No incumbent in tie, using momentum approach: {best_asset}")
                return [best_asset] if best_asset else []
            else:
                # For multi-asset, use the original top_assets (momentum approach)
                logging.info(f"[TIE-BREAKING] No incumbents in tie, using momentum approach: {top_assets}")
                return top_assets

    def _apply_incumbent_tie_breaking_weighted(self, scores, current_holdings, top_assets, asset_weights, mtpi_signal):
        """Apply incumbent tie-breaking logic for weighted allocation."""
        if not scores or not current_holdings or not top_assets:
            return top_assets, asset_weights

        # Find the maximum score
        max_score = max(scores.values())
        tied_assets = [asset for asset, score in scores.items() if score == max_score]

        # If there's no tie, return the original selection
        if len(tied_assets) <= 1:
            return top_assets, asset_weights

        # Check if any currently held assets are among the tied assets
        current_assets = set(current_holdings.keys())
        tied_incumbents = current_assets.intersection(set(tied_assets))

        if tied_incumbents:
            # Keep the incumbent(s) that are tied for the top score
            incumbents_to_keep = list(tied_incumbents)[:self.n_assets]
            remaining_slots = self.n_assets - len(incumbents_to_keep)

            if remaining_slots > 0:
                # Fill remaining slots with non-incumbent tied assets
                non_incumbent_tied = [asset for asset in tied_assets if asset not in tied_incumbents]
                additional_assets = non_incumbent_tied[:remaining_slots]
                final_assets = incumbents_to_keep + additional_assets
            else:
                final_assets = incumbents_to_keep

            # Rebuild asset_weights with the new asset selection
            new_asset_weights = {}
            for i, asset in enumerate(final_assets):
                if i < len(self.weights):
                    new_asset_weights[asset] = self.weights[i]

            logging.info(f"[TIE-BREAKING] Incumbent approach (weighted): Keeping incumbents {incumbents_to_keep} from tied assets {tied_assets}")
            return final_assets, new_asset_weights
        else:
            # No incumbents in tied assets, use momentum approach (original selection)
            logging.info(f"[TIE-BREAKING] No incumbents in tie, using momentum approach (weighted): {top_assets}")
            return top_assets, asset_weights

    def fetch_data(self):
        """Fetch OHLCV data for the selected assets, DexScreener tokens, and GeckoTerminal tokens."""
        # Calculate fetch start date with appropriate warmup period
        analysis_date = datetime.strptime(self.analysis_start_date, '%Y-%m-%d')

        # Determine warmup days based on timeframe and allocation method
        # For weighted allocation, we need more warmup days to ensure proper signal generation
        if self.use_weighted_allocation:
            # Extract the timeframe interval
            import re
            tf_match = re.match(r'(\d+)([mhdwM])', self.timeframe)

            if tf_match:
                tf_number, tf_unit = int(tf_match.group(1)), tf_match.group(2)

                # Adjust warmup days based on timeframe unit
                if tf_unit == 'm':  # minutes
                    warmup_days = 90  # More days for higher frequency data
                elif tf_unit == 'h':  # hours
                    # For hourly data, consider both the indicator length and frequency
                    # For 1h -> 90 days, 4h -> 75 days, 12h -> 60 days
                    warmup_days = max(60, 90 - (tf_number * 2.5))
                elif tf_unit == 'd':  # days
                    warmup_days = 120  # For daily data, go back at least 60 days
                elif tf_unit == 'w':  # weeks
                    warmup_days = 40  # For weekly data, go back 40 days
                else:  # months or other
                    warmup_days = 90  # Default to 90 days
            else:
                warmup_days = 90  # Default to 90 days if we can't parse the timeframe

            logging.info(f"Using extended warmup period of {warmup_days} days for weighted allocation with {self.timeframe} timeframe")
        else:
            # Standard warmup for equal allocation
            warmup_days = 60
            logging.info(f"Using standard warmup period of {warmup_days} days for equal allocation")

        fetch_start_date = (analysis_date - timedelta(days=warmup_days)).strftime('%Y-%m-%d')
        logging.info(f"Fetching data from {fetch_start_date} to ensure proper warmup (analysis starts at {self.analysis_start_date})")

        # Fetch data for regular exchange assets
        raw_data_dict = fetch_ohlcv_data(
            exchange_id=self.exchange_id,
            symbols=self.selected_assets,
            timeframe=self.timeframe,
            since=fetch_start_date,
            use_cache=self.use_cache,
            force_refresh=self.force_refresh,
            ensure_data_since=True  # Ensure data goes back to the analysis start date
        )



        # Fetch data for GeckoTerminal tokens if available
        if GECKOTERMINAL_AVAILABLE and self.geckoterminal_tokens:
            logging.warning(f"Fetching data for {len(self.geckoterminal_tokens)} GeckoTerminal tokens")

            for token_info in self.geckoterminal_tokens:
                network = token_info.get('network')
                token_address = token_info.get('token_address')
                symbol = token_info.get('symbol')

                if not all([network, token_address, symbol]):
                    logging.error(f"Invalid GeckoTerminal token info: {token_info}. Must include network, token_address, and symbol.")
                    continue

                try:
                    # Special handling for AUTISM token
                    if symbol == 'AUTISM/USDT':
                        try:
                            # Try to use the improved fetcher first
                            # from src.geckoterminal_improved import fetch_with_improved_pagination  # Module not available
                            raise ImportError("geckoterminal_improved not available")
                            logging.warning(f"Using improved GeckoTerminal fetcher with enhanced pagination for {symbol}")

                            df = fetch_with_improved_pagination(
                                network=network,
                                token_address=token_address,
                                timeframe=self.timeframe,
                                max_pages=10,  # Fetch up to 10 pages of data
                                page_delay=1.0,
                                use_cache=self.use_cache,
                                force_refresh=self.force_refresh,
                                context=self.context  # Pass context for rate limiting
                            )
                        except ImportError:
                            # Fall back to other fetchers
                            logging.warning(f"Improved GeckoTerminal fetcher not available for {symbol}, trying enhanced fetcher")
                            try:
                                # from src.geckoterminal_enhanced import fetch_token_with_pagination  # Module not available
                                raise ImportError("geckoterminal_enhanced not available")
                                df = fetch_token_with_pagination(
                                    network=network,
                                    token_address=token_address,
                                    timeframe=self.timeframe,
                                    max_pages=10,
                                    page_delay=1.0,
                                    use_cache=self.use_cache,
                                    force_refresh=self.force_refresh
                                )
                            except ImportError:
                                # Fall back to the original fetcher
                                logging.warning(f"Enhanced GeckoTerminal fetcher not available for {symbol}, using standard fetcher")
                                df = fetch_geckoterminal_data(
                                    network=network,
                                    token_address=token_address,
                                    timeframe=self.timeframe,
                                    since=fetch_start_date,
                                    use_cache=self.use_cache,
                                    context=self.context  # Pass context for rate limiting
                                )
                    else:
                        # For other tokens, use the regular fetching logic
                        try:
                            # from src.geckoterminal_enhanced import fetch_token_with_pagination  # Module not available
                            raise ImportError("geckoterminal_enhanced not available")
                            logging.info(f"Using enhanced GeckoTerminal fetcher with pagination for {symbol}")

                            df = fetch_token_with_pagination(
                                network=network,
                                token_address=token_address,
                                timeframe=self.timeframe,
                                max_pages=10,  # Fetch up to 10 pages of data
                                page_delay=1.0,
                                use_cache=self.use_cache,
                                force_refresh=self.force_refresh
                            )
                        except ImportError:
                            # Fall back to the original fetcher
                            logging.info(f"Enhanced GeckoTerminal fetcher not available, using standard fetcher for {symbol}")
                            df = fetch_geckoterminal_data(
                                network=network,
                                token_address=token_address,
                                timeframe=self.timeframe,
                                since=fetch_start_date,
                                use_cache=self.use_cache,
                                context=self.context  # Pass context for rate limiting
                            )

                    if not df.empty:
                        logging.warning(f"Successfully fetched data for {symbol} from GeckoTerminal: {len(df)} candles from {df.index.min().date()} to {df.index.max().date()}")
                        raw_data_dict[symbol] = df
                    else:
                        logging.error(f"Failed to fetch data for {symbol} from GeckoTerminal")
                except Exception as e:
                    logging.error(f"Error fetching {symbol} from GeckoTerminal: {e}")

        # Filter Autism token data to start from July 19, 2024 (skipping the first 3 days)
        if 'AUTISM/USDT' in raw_data_dict:
            autism_df = raw_data_dict['AUTISM/USDT']
            if not autism_df.empty:
                # Define the cutoff date (July 19, 2024)
                cutoff_date = pd.Timestamp('2024-07-19', tz='UTC')

                # Get the original start date for logging
                original_start_date = autism_df.index.min()

                # Filter the dataframe to only include dates on or after the cutoff date
                filtered_df = autism_df[autism_df.index >= cutoff_date]

                if len(filtered_df) < len(autism_df):
                    logging.warning(f"Filtered AUTISM/USDT data to start from {cutoff_date.date()} instead of {original_start_date.date()}")
                    logging.warning(f"Removed {len(autism_df) - len(filtered_df)} candles from the beginning of the data")
                    raw_data_dict['AUTISM/USDT'] = filtered_df

        # Align dataframes
        self.data_dict = self.align_dataframes(raw_data_dict, self.timeframe)

    def fetch_mtpi_signals(self):
        """Fetch MTPI signals."""
        # Calculate fetch start date for MTPI with appropriate warmup
        analysis_date = datetime.strptime(self.analysis_start_date, '%Y-%m-%d')

        # Determine MTPI warmup days based on timeframe and allocation method
        # For weighted allocation, we need more warmup days to ensure proper signal generation
        if self.use_weighted_allocation:
            # Extract the timeframe interval
            import re
            mtpi_match = re.match(r'(\d+)([mhdwM])', self.mtpi_timeframe)

            if mtpi_match:
                mtpi_number, mtpi_unit = int(mtpi_match.group(1)), mtpi_match.group(2)

                # Adjust warmup days based on timeframe unit
                if mtpi_unit == 'm':  # minutes
                    mtpi_warmup_days = 120  # Increased back to 120 days for proper MTPI crossover detection
                elif mtpi_unit == 'h':  # hours
                    # For hourly data, consider both the indicator length and frequency
                    mtpi_warmup_days = max(120, 120 - (mtpi_number * 1))  # Increased base to 120 days
                elif mtpi_unit == 'd':  # days
                    mtpi_warmup_days = 120  # Increased back to 120 days for proper MTPI crossover detection
                elif mtpi_unit == 'w':  # weeks
                    mtpi_warmup_days = 120  # Increased to 120 days for proper MTPI crossover detection
                else:  # months or other
                    mtpi_warmup_days = 120  # Increased back to 120 days for proper MTPI crossover detection
            else:
                mtpi_warmup_days = 120  # Increased back to 120 days for proper MTPI crossover detection

            logging.info(f"Using extended MTPI warmup period of {mtpi_warmup_days} days for weighted allocation with {self.mtpi_timeframe} timeframe")
        else:
            # Standard warmup for equal allocation
            mtpi_warmup_days = 120  # Increased back to 120 days for proper MTPI crossover detection
            logging.info(f"Using standard MTPI warmup period of {mtpi_warmup_days} days")

        mtpi_fetch_start_date = (analysis_date - timedelta(days=mtpi_warmup_days)).strftime('%Y-%m-%d')
        logging.info(f"Fetching MTPI signals from {mtpi_fetch_start_date} to ensure proper warmup (analysis starts at {self.analysis_start_date})")

        # Get the current trend method from config
        trend_method = self.get_trend_method()

        # Log the trend method being used for MTPI signals
        logging.info(f"Fetching MTPI signals using trend method: {trend_method}")

        # Fetch MTPI signals using multi-indicator approach if indicators are specified
        if self.mtpi_indicators or self.mtpi_combination_method or self.mtpi_long_threshold is not None or self.mtpi_short_threshold is not None:
            # Use the new multi-indicator MTPI signal handler with command line overrides
            from src.MTPI_signal_handler import fetch_multi_indicator_mtpi_signal_with_config_override

            # Create config override dictionary
            config_override = {}
            if self.mtpi_indicators:
                config_override['enabled_indicators'] = self.mtpi_indicators
            if self.mtpi_combination_method:
                config_override['combination_method'] = self.mtpi_combination_method
            if self.mtpi_long_threshold is not None:
                config_override['long_threshold'] = self.mtpi_long_threshold
            if self.mtpi_short_threshold is not None:
                config_override['short_threshold'] = self.mtpi_short_threshold

            logging.info(f"Using multi-indicator MTPI with config override: {config_override}")

            # Fetch historical signals with config override
            self.mtpi_signals = fetch_multi_indicator_mtpi_signal_with_config_override(
                timeframe=self.mtpi_timeframe,
                since=mtpi_fetch_start_date,
                config_override=config_override,
                use_cache=self.use_cache
            )
        else:
            # Use the original single-indicator approach (backward compatibility)
            self.mtpi_signals = fetch_historical_mtpi_signals(
                timeframe=self.mtpi_timeframe,
                length=35,  # Default PGO length
                upper_threshold=1.1,  # Default upper threshold
                lower_threshold=-0.58,  # Default lower threshold
                skip_warmup=False,  # Changed to False to match TradingView behavior
                since=mtpi_fetch_start_date,
                use_cache=self.use_cache
            )

    def get_trend_method(self):
        """Get the current trend method."""
        return self.trend_method

    def calculate_scores(self):
        """Calculate daily scores for asset ranking."""
        logging.info(f"Calculating daily scores using trend method: {self.trend_method}...")

        # Add placeholder column for compatibility
        for symbol in self.data_dict:
            self.data_dict[symbol][RSI_TV_SMA_SIGNAL_COL] = 0.0

        # Always update config with the current trend method
        if 'settings' not in self.config:
            self.config['settings'] = {}

        # Update the config with the trend method and PGO parameters
        self.config['settings']['trend_method'] = self.trend_method

        # Update PGO parameters in config if they are provided
        if self.pgo_length is not None:
            self.config['settings']['pgo_length'] = self.pgo_length
            logging.info(f"Updated config with PGO length: {self.pgo_length}")

        if self.pgo_upper_threshold is not None:
            self.config['settings']['pgo_upper_threshold'] = self.pgo_upper_threshold
            logging.info(f"Updated config with PGO upper threshold: {self.pgo_upper_threshold}")

        if self.pgo_lower_threshold is not None:
            self.config['settings']['pgo_lower_threshold'] = self.pgo_lower_threshold
            logging.info(f"Updated config with PGO lower threshold: {self.pgo_lower_threshold}")

        # Save the updated config
        from src.config_manager import save_config
        save_config(self.config)
        logging.info(f"Updated config with trend method: {self.trend_method} and PGO parameters")

        # Check if matrices should be disabled (from command line args)
        debug_matrices = not getattr(self, 'no_matrices', False)

        # Calculate daily scores
        self.daily_scores_df = calculate_daily_scores(
            self.data_dict,
            pgo_signal_col=RSI_TV_SMA_SIGNAL_COL,
            use_mtpi_signal=self.use_mtpi,
            mtpi_signals=self.mtpi_signals if self.use_mtpi else None,
            debug_matrices=debug_matrices,
            ratio_calculation=self.ratio_calculation
        )

    def run_strategy_with_detailed_logging(self):
        """Run the strategy with detailed logging of allocation decisions."""
        if self.use_weighted_allocation:
            weights_str = ", ".join([f"{w*100:.1f}%" for w in self.weights])
            logging.warning(f"Running strategy with n_assets={self.n_assets}, weighted allocation ({weights_str}), MTPI filtering {'ENABLED' if self.use_mtpi else 'DISABLED'}")
        else:
            logging.warning(f"Running strategy with n_assets={self.n_assets}, equal allocation, MTPI filtering {'ENABLED' if self.use_mtpi else 'DISABLED'}")

        # Start timing
        self.start_time = time.time()

        # Log the MTPI signal handling approach - only log warnings for relaxed mode
        if not self.wait_for_confirmed_signals:
            logging.warning("WARNING: Using RELAXED MODE - May use signals from partially formed candles")
            logging.warning("This may lead to premature trading decisions based on signals that could change")

        # Find common dates across all assets
        common_dates = sorted(set.intersection(*[set(df.index) for df in self.data_dict.values()]))

        # Log the date range for each asset to help diagnose issues
        logging.info("Date ranges for each asset:")
        for symbol, df in self.data_dict.items():
            if not df.empty:
                logging.info(f"  {symbol}: {df.index.min().date()} to {df.index.max().date()} ({len(df)} candles)")

        # Log the common dates range
        if common_dates:
            logging.info(f"Common dates range: {common_dates[0].date()} to {common_dates[-1].date()} ({len(common_dates)} candles)")
        else:
            logging.warning("No common dates found across all assets!")

        # Convert analysis start date to timestamp with UTC timezone
        strategy_start = pd.Timestamp(self.analysis_start_date, tz='UTC')

        # Convert analysis end date to timestamp with UTC timezone (if provided)
        strategy_end = None
        end_idx = len(common_dates) - 1  # Default to last available date
        if self.analysis_end_date:
            strategy_end = pd.Timestamp(self.analysis_end_date, tz='UTC')

        # Find the strategy start index - we want to start EXACTLY on the specified date
        start_idx = 0
        exact_match_found = False

        # First try to find an exact match for the start date
        for i, date in enumerate(common_dates):
            # Convert to string format for comparison (ignoring time)
            date_str = date.strftime('%Y-%m-%d')
            if date_str == self.analysis_start_date:
                start_idx = i
                exact_match_found = True
                break

        # If no exact match, find the first date >= strategy_start
        if not exact_match_found:
            logging.warning(f"No exact match found for start date: {self.analysis_start_date}. Using closest date after it.")
            for i, date in enumerate(common_dates):
                # Ensure date is timezone-aware for comparison
                if date.tz is None:
                    date = date.tz_localize('UTC')
                if date >= strategy_start:
                    start_idx = i
                    break

        # Find the strategy end index if analysis_end_date is provided
        if strategy_end:
            end_exact_match_found = False
            # First try to find an exact match for the end date
            for i, date in enumerate(common_dates):
                # Convert to string format for comparison (ignoring time)
                date_str = date.strftime('%Y-%m-%d')
                if date_str == self.analysis_end_date:
                    end_idx = i
                    end_exact_match_found = True
                    break

            # If no exact match, find the last date <= strategy_end
            if not end_exact_match_found:
                logging.warning(f"No exact match found for end date: {self.analysis_end_date}. Using closest date before it.")
                for i in range(len(common_dates) - 1, -1, -1):
                    date = common_dates[i]
                    # Ensure date is timezone-aware for comparison
                    if date.tz is None:
                        date = date.tz_localize('UTC')
                    if date <= strategy_end:
                        end_idx = i
                        break

            logging.info(f"Analysis will end at index {end_idx}: {common_dates[end_idx].date()}")

        # Ensure end_idx is not before start_idx
        if end_idx < start_idx:
            logging.error(f"Analysis end date ({self.analysis_end_date}) is before start date ({self.analysis_start_date})")
            raise ValueError(f"Analysis end date must be after start date")

        # Log the analysis date range
        if self.analysis_end_date:
            logging.info(f"Analysis will be limited to: {common_dates[start_idx].date()} to {common_dates[end_idx].date()} ({end_idx - start_idx + 1} candles)")
        else:
            logging.info(f"Analysis will run from: {common_dates[start_idx].date()} to {common_dates[-1].date()} ({len(common_dates) - start_idx} candles)")

        # Initialize equity curve - use NaN for dates before the analysis start date
        # but keep the full date range for charting purposes
        equity_curve = pd.Series(index=common_dates, dtype=float)
        for i in range(len(common_dates)):
            if i < start_idx:
                # Use NaN for dates before the analysis start date
                equity_curve.iloc[i] = float('nan')
            elif i == start_idx:
                # Set the initial capital at the analysis start date
                equity_curve.iloc[i] = self.initial_capital
            else:
                # Will be calculated in the main loop
                pass

        # Initialize assets held DataFrame
        assets_held = pd.DataFrame(index=common_dates, columns=self.selected_assets, dtype=float).fillna(0.0)

        # Calculate daily returns for each asset based on execution timing
        asset_returns = {}

        # Log execution timing verification
        logging.info(f"EXECUTION TIMING VERIFICATION:")
        logging.info(f"   Execution Method: {self.execution_timing}")
        if self.execution_timing == 'candle_close':
            logging.info(f"   Automatic execution at 00:00 UTC (candle close)")
            logging.info(f"   Signal generated and executed immediately")
        elif self.execution_timing == 'manual_12pm':
            logging.info(f"   Manual execution at 12:00 PM UTC")
            logging.info(f"   12-hour delay between signal and execution")

        for symbol, df in self.data_dict.items():
            if self.execution_timing == 'candle_close':
                # Standard execution: Calculate returns from previous close to current close
                # This matches TradingView's approach to calculating returns
                # Formula: (close_t - close_t-1) / close_t-1
                returns = df['close'].pct_change().fillna(0.0)
            elif self.execution_timing == 'manual_12pm':
                # Manual execution: Calculate returns from previous close to 12 PM UTC price
                returns = self._calculate_manual_execution_returns(symbol, df)
            else:
                # Default to candle close if unknown timing
                logging.warning(f"Unknown execution timing '{self.execution_timing}', defaulting to candle_close")
                returns = df['close'].pct_change().fillna(0.0)

            # No need to shift returns as pct_change() already gives us the change from the previous period
            asset_returns[symbol] = returns

        # Initialize tracking variables
        # Always use a dictionary for current_holdings
        # For weighted allocation, it will store asset:weight pairs
        # For equal allocation, it will store asset:1.0 pairs (we'll handle the normalization later)
        current_holdings = {}
        mtpi_filtering_stats = {
            "daysInMarket": 0,
            "daysOutOfMarket": 0,
            "totalDays": 0
        }

        # Main strategy loop
        for i in range(1, len(common_dates)):
            today = common_dates[i]
            yesterday = common_dates[i-1]

            # Debug logging for key dates
            if today.strftime('%Y-%m-%d') in ['2024-01-06', '2024-01-07']:
                logging.error(f"🚨 [MAIN LOOP] Processing date: {today}, i={i}")

            # Skip dates before strategy start
            if i <= start_idx:
                continue

            # Stop processing dates after strategy end
            if i > end_idx:
                break

            # Get MTPI signal for yesterday
            mtpi_allows_trade = True
            mtpi_signal_value = None

            # Handle MTPI signals - this complex logic ensures proper timeframe alignment
            # The signal used here will also be used for asset selection and trading decisions
            if self.use_mtpi and self.mtpi_signals is not None:
                # Extract the timeframe interval from the MTPI timeframe
                import re
                mtpi_match = re.match(r'(\d+)([mhdwM])', self.mtpi_timeframe)

                if mtpi_match:
                    mtpi_number, mtpi_unit = int(mtpi_match.group(1)), mtpi_match.group(2)

                    # Find the most recent MTPI candle close time before the current timestamp
                    if mtpi_unit == 'h':  # Hourly timeframes
                        # For hourly timeframes, we need to find the most recent COMPLETED candle
                        # For example, with 4h timeframe, candles close at 00:00, 04:00, 08:00, 12:00, 16:00, 20:00
                        hours_since_day_start = today.hour

                        # Calculate the current candle start hour (which might still be forming)
                        current_candle_hour = (hours_since_day_start // mtpi_number) * mtpi_number

                        # If we're enforcing strict signal confirmation (recommended)
                        if self.wait_for_confirmed_signals:
                            # We need to use the previous fully completed candle, not the current one that might be forming
                            # This is the key change to prevent trading on premature signals

                            # Calculate the previous completed candle's hour
                            if current_candle_hour == 0:
                                # If we're in the first candle of the day, use the last candle from yesterday
                                hours_in_day = 24
                                last_candle_hour_yesterday = ((hours_in_day - mtpi_number) // mtpi_number) * mtpi_number
                                signal_time = (today.replace(hour=0, minute=0) -
                                              pd.Timedelta(hours=mtpi_number))
                            else:
                                # Otherwise, use the previous completed candle from today
                                signal_time = today.replace(hour=current_candle_hour - mtpi_number, minute=0)
                        else:
                            # If we're exactly at a candle boundary (e.g., 00:00, 04:00, etc.),
                            # the candle is just starting, not completed
                            if hours_since_day_start == current_candle_hour and today.minute == 0:
                                # We're at the start of a new candle, so we need the previous completed candle
                                if current_candle_hour == 0:
                                    # If we're at midnight, we need yesterday's last completed candle
                                    hours_in_day = 24
                                    last_candle_hour_yesterday = ((hours_in_day - mtpi_number) // mtpi_number) * mtpi_number
                                    signal_time = (today.replace(hour=0, minute=0) -
                                                  pd.Timedelta(hours=mtpi_number))
                                else:
                                    # Otherwise, use the previous completed candle
                                    signal_time = today.replace(hour=current_candle_hour - mtpi_number, minute=0)
                            else:
                                # We're between candle boundaries
                                # Use the most recent completed candle, NOT the currently forming one
                                if current_candle_hour == 0 and hours_since_day_start > 0:
                                    # If we've passed midnight but haven't reached the first candle close of the day
                                    # Use yesterday's last completed candle
                                    hours_in_day = 24
                                    last_candle_hour_yesterday = ((hours_in_day - mtpi_number) // mtpi_number) * mtpi_number
                                    signal_time = (today.replace(hour=0, minute=0) -
                                                  pd.Timedelta(hours=hours_in_day - last_candle_hour_yesterday))
                                else:
                                    # Use the most recent completed candle
                                    # If we're in the middle of a candle formation, use the previous completed candle
                                    signal_time = today.replace(hour=current_candle_hour, minute=0)

                    elif mtpi_unit == 'd':  # Daily timeframes
                        if self.wait_for_confirmed_signals:
                            # In strict mode, always use the previous day's completed candle
                            # This ensures we're only trading on confirmed signals
                            signal_time = today.replace(hour=0, minute=0) - pd.Timedelta(days=1)
                        else:
                            # In relaxed mode, we might use today's signal if we're past midnight
                            if today.hour == 0 and today.minute == 0:
                                # If we're exactly at midnight, use yesterday's signal
                                signal_time = today - pd.Timedelta(days=1)
                            else:
                                # Otherwise, use today's signal (from 00:00)
                                signal_time = today.replace(hour=0, minute=0)

                    else:  # Other timeframes (minutes, weeks, months)
                        # For now, default to using the previous timestamp
                        signal_time = yesterday
                        logging.warning(f"MTPI timeframe unit '{mtpi_unit}' not fully implemented. Using previous timestamp.")

                    # Find the closest MTPI signal on or before the signal_time
                    prev_signals = self.mtpi_signals[self.mtpi_signals.index <= signal_time]

                    if not prev_signals.empty:
                        mtpi_signal_value = prev_signals.iloc[-1]
                        mtpi_allows_trade = (mtpi_signal_value == 1)  # 1 is bullish
                    else:
                        # No valid signals found before this date
                        logging.warning(f"No valid MTPI signals found before {signal_time}")
                        mtpi_signal_value = 1  # Default to bullish if no signal available
                        mtpi_allows_trade = True

                else:
                    # Couldn't parse the MTPI timeframe, use a simple fallback
                    logging.warning(f"Couldn't parse MTPI timeframe '{self.mtpi_timeframe}'. Using simple lookup.")

                    # Find the closest previous signal
                    prev_signals = self.mtpi_signals[self.mtpi_signals.index <= yesterday]
                    if not prev_signals.empty:
                        mtpi_signal_value = prev_signals.iloc[-1]
                        mtpi_allows_trade = (mtpi_signal_value == 1)  # 1 is bullish
                    else:
                        # No valid signals found
                        logging.warning(f"No valid MTPI signals found before {today}")
                        mtpi_signal_value = 1  # Default to bullish if no signal available
                        mtpi_allows_trade = True

                # Update MTPI filtering stats
                mtpi_filtering_stats["totalDays"] += 1
                if mtpi_allows_trade:
                    mtpi_filtering_stats["daysInMarket"] += 1
                else:
                    mtpi_filtering_stats["daysOutOfMarket"] += 1

            # Get scores for asset selection based on execution timing
            # For automatic execution: use yesterday's scores (signal generated and executed at 00:00 UTC)
            # For manual execution: use yesterday's scores but with 12-hour delay consideration
            if self.execution_timing == 'candle_close':
                # Automatic execution: use yesterday's scores (traditional approach)
                score_date = yesterday
                yesterday_scores = self.daily_scores_df.loc[score_date] if score_date in self.daily_scores_df.index else pd.Series()
            elif self.execution_timing == 'manual_12pm':
                # Manual execution: signal generated at yesterday 00:00 UTC, but executed at today 12:00 PM UTC
                # This means we're making decisions based on yesterday's close but executing 12 hours later
                # The asset selection should still be based on yesterday's scores, but the execution timing affects returns
                score_date = yesterday
                yesterday_scores = self.daily_scores_df.loc[score_date] if score_date in self.daily_scores_df.index else pd.Series()
            else:
                # Default to yesterday's scores
                score_date = yesterday
                yesterday_scores = self.daily_scores_df.loc[score_date] if score_date in self.daily_scores_df.index else pd.Series()

            # Filter out assets that don't exist yet at this date
            filtered_scores = {}
            logging.error(f"🚨 [RAW SCORES] daily_scores_df at {score_date}: {yesterday_scores.to_dict()}")
            for asset, score in yesterday_scores.to_dict().items():
                # Only include assets that exist at this date
                if asset in self.asset_first_dates and score_date >= self.asset_first_dates[asset]:
                    filtered_scores[asset] = score
                    # Add debug logging for the first few days to understand which assets are being included
                    if i <= start_idx + 5:  # Only log for the first 5 days after start
                        logging.info(f"Including {asset} on {score_date} with score {score} (first date: {self.asset_first_dates.get(asset, 'unknown')})")
                else:
                    # Asset doesn't exist yet, set score to 0
                    filtered_scores[asset] = 0
                    # Add debug logging for the first few days to understand which assets are being filtered out
                    if i <= start_idx + 5:  # Only log for the first 5 days after start
                        logging.info(f"Filtered out {asset} on {score_date} because it doesn't exist yet (first date: {self.asset_first_dates.get(asset, 'unknown')})")

            # Find the top N assets for yesterday using filtered scores
            # Use the mtpi_signal_value already determined by the timeframe-based logic above

            # Get previous day's scores for momentum calculation
            previous_scores = {}
            if i > start_idx and self.tie_breaking_strategy == 'momentum':
                # Calculate the actual previous date instead of using index-based lookup
                from datetime import timedelta
                prev_score_date = yesterday - timedelta(days=1)
                logging.info(f"[MOMENTUM DEBUG] Today: {today}, Yesterday: {yesterday}, i: {i}, start_idx: {start_idx}, prev_score_date: {prev_score_date}")

                # Get raw previous scores from daily_scores_df
                prev_raw_scores = self.daily_scores_df.loc[prev_score_date] if prev_score_date in self.daily_scores_df.index else pd.Series()
                logging.error(f"🚨 [RAW SCORES] daily_scores_df at {prev_score_date}: {prev_raw_scores.to_dict()}")

                # Apply the same filtering logic as used for current scores
                for asset, score in prev_raw_scores.to_dict().items():
                    # Only include assets that exist at the previous date
                    if asset in self.asset_first_dates and prev_score_date >= self.asset_first_dates[asset]:
                        previous_scores[asset] = score
                        logging.info(f"[MOMENTUM DEBUG] {asset}: {score} (filtered, from {prev_score_date})")
                    else:
                        # Asset doesn't exist yet, set score to 0
                        previous_scores[asset] = 0
                        logging.info(f"[MOMENTUM DEBUG] {asset}: 0 (filtered out, from {prev_score_date})")

                logging.info(f"[MOMENTUM DEBUG] Retrieved filtered previous_scores: {previous_scores}")
                logging.info(f"[MOMENTUM DEBUG] Current filtered_scores will be: {filtered_scores}")
            else:
                logging.debug(f"[MOMENTUM DEBUG] Skipping momentum logic: i={i}, start_idx={start_idx}, strategy={self.tie_breaking_strategy}")

            logging.error(f"🚨 [MAIN] Asset selection path: use_weighted_allocation={self.use_weighted_allocation}")
            if self.use_weighted_allocation:
                # Use weighted allocation
                if self.tie_breaking_strategy == 'momentum' and previous_scores:
                    # Use momentum-based selection
                    from src.momentum_scoring import find_top_n_assets_with_momentum_and_weights
                    top_assets, asset_weights = find_top_n_assets_with_momentum_and_weights(
                        filtered_scores,
                        previous_scores,
                        n=self.n_assets,
                        mtpi_signal=mtpi_signal_value,
                        weights=self.weights
                    )
                else:
                    # Use standard selection
                    from src.scoring_n import find_top_n_assets_with_weights
                    top_assets, asset_weights = find_top_n_assets_with_weights(
                        filtered_scores,
                        n=self.n_assets,
                        mtpi_signal=mtpi_signal_value,
                        weights=self.weights,
                        verbose=False  # Set to False to improve performance
                    )

                # Apply tie-breaking strategy for weighted allocation
                if self.tie_breaking_strategy == 'incumbent' and current_holdings and filtered_scores:
                    top_assets, asset_weights = self._apply_incumbent_tie_breaking_weighted(
                        filtered_scores, current_holdings, top_assets, asset_weights, mtpi_signal_value
                    )

                # Determine new holdings with weights
                new_holdings = asset_weights if mtpi_allows_trade else {}
            else:
                # Use equal allocation
                logging.error(f"🚨 [MAIN] Using equal allocation path")
                logging.error(f"🚨 [MAIN] Checking momentum condition: tie_breaking_strategy={self.tie_breaking_strategy}, previous_scores={bool(previous_scores)}")
                if self.tie_breaking_strategy == 'momentum' and previous_scores:
                    # Use momentum-based selection
                    from src.momentum_scoring import find_top_n_assets_with_momentum
                    top_assets = find_top_n_assets_with_momentum(
                        filtered_scores,
                        previous_scores,
                        n=self.n_assets,
                        mtpi_signal=mtpi_signal_value
                    )
                    logging.info(f"[DEBUG] Momentum selection returned: {top_assets}")
                else:
                    # Use standard selection
                    top_assets = find_top_n_assets_for_day(filtered_scores, n=self.n_assets, mtpi_signal=mtpi_signal_value)

                # Apply tie-breaking strategy for equal allocation (only for incumbent, momentum already handled above)
                if self.tie_breaking_strategy == 'incumbent' and current_holdings and filtered_scores:
                    logging.info(f"[DEBUG] Applying incumbent tie-breaking: strategy={self.tie_breaking_strategy}")
                    top_assets = self._apply_incumbent_tie_breaking_equal(
                        filtered_scores, current_holdings, top_assets, mtpi_signal_value
                    )
                else:
                    logging.info(f"[DEBUG] NOT applying incumbent tie-breaking: strategy={self.tie_breaking_strategy}, current_holdings={bool(current_holdings)}, filtered_scores={bool(filtered_scores)}")
                # Note: When tie_breaking_strategy == 'momentum', momentum logic was already applied above,
                # so we don't need additional tie-breaking here

                # Determine new holdings as a dictionary with equal weights
                if mtpi_allows_trade and top_assets:
                    weight = 1.0 / len(top_assets)
                    new_holdings = {asset: weight for asset in top_assets}
                else:
                    new_holdings = {}

            # PORTFOLIO DRIFT REBALANCING LOGIC
            # Check if current portfolio has drifted from target weights and needs rebalancing
            portfolio_needs_rebalancing = False
            rebalancing_reason = ""

            # PORTFOLIO DRIFT REBALANCING LOGIC (only when enabled)
            if self.enable_rebalancing and self.use_weighted_allocation and current_holdings and new_holdings and mtpi_allows_trade:
                # Check if the same assets are still the top performers with the same target weights
                same_assets = set(current_holdings.keys()) == set(new_holdings.keys())
                same_target_weights = False

                if same_assets:
                    # Check if the target weights are also the same (same rankings)
                    same_target_weights = True
                    for asset in current_holdings.keys():
                        if abs(current_holdings[asset] - new_holdings[asset]) > 0.001:  # Small threshold for floating point errors
                            same_target_weights = False
                            break

                # Only check for portfolio drift when we have the same assets with same target weights
                if same_assets and same_target_weights:
                    # Same assets with same target weights, check for weight drift due to market movements
                    # Calculate current portfolio weights after market movements
                    current_portfolio_value = equity_curve.iloc[i-1] if i > start_idx else self.initial_capital

                    # Apply today's returns to get current market values
                    current_market_values = {}
                    total_current_value = 0.0

                    for asset, weight in current_holdings.items():
                        if asset in asset_returns and today in asset_returns[asset].index:
                            # Calculate current market value after today's price movement
                            asset_return = asset_returns[asset].loc[today]
                            current_value = (current_portfolio_value * weight) * (1 + asset_return)
                            current_market_values[asset] = current_value
                            total_current_value += current_value

                    # Calculate current weights based on market values
                    current_weights = {}
                    if total_current_value > 0:
                        for asset, value in current_market_values.items():
                            current_weights[asset] = value / total_current_value

                    # Check for weight drift only when we have the same assets with same target weights
                    max_drift = 0.0
                    for asset in new_holdings.keys():
                        if asset in current_weights:
                            target_weight = new_holdings[asset]
                            current_weight = current_weights[asset]
                            drift = abs(current_weight - target_weight)
                            max_drift = max(max_drift, drift)

                    if max_drift > self.rebalance_threshold:
                        portfolio_needs_rebalancing = True
                        rebalancing_reason = f"Portfolio drift ({max_drift:.1%}) exceeds threshold ({self.rebalance_threshold:.1%})"

                        # Print and log the drift details
                        print(f"\nPORTFOLIO DRIFT REBALANCING TRIGGERED on {today.date()}")
                        print(f"Reason: {rebalancing_reason}")
                        print(f"Asset Weight Drift Details:")

                        for asset in new_holdings.keys():
                            if asset in current_weights:
                                target_weight = new_holdings[asset]
                                current_weight = current_weights[asset]
                                drift = abs(current_weight - target_weight)
                                drift_direction = "UP" if current_weight > target_weight else "DOWN"
                                print(f"   {asset}: {current_weight:.1%} -> {target_weight:.1%} (drift: {drift_direction} {drift:.1%})")

                        # Log the drift details
                        if i <= start_idx + 10 or max_drift > 0.1:  # Log first 10 days or significant drifts
                            logging.info(f"Portfolio drift detected on {today.date()}: {rebalancing_reason}")
                            for asset in new_holdings.keys():
                                if asset in current_weights:
                                    target_weight = new_holdings[asset]
                                    current_weight = current_weights[asset]
                                    drift = abs(current_weight - target_weight)
                                    logging.info(f"  {asset}: target={target_weight:.1%}, current={current_weight:.1%}, drift={drift:.1%}")
                # Note: Normal asset changes (different assets or different weights) are NOT treated as rebalancing events
                # They are handled as regular portfolio updates outside this conditional block

            # Calculate the portfolio return based on current holdings
            portfolio_return = 0.0

            # Calculate transaction costs for assets being sold and bought
            transaction_cost = 0.0

            # Identify assets to sell and buy
            # Both current_holdings and new_holdings are dictionaries
            assets_to_sell = set(current_holdings.keys()) - set(new_holdings.keys())
            assets_to_buy = set(new_holdings.keys()) - set(current_holdings.keys())

            if self.use_weighted_allocation:
                # For weighted allocation, also identify assets whose weights have changed
                assets_with_changed_weights = set()
                for asset in set(current_holdings.keys()) & set(new_holdings.keys()):
                    if abs(current_holdings[asset] - new_holdings[asset]) > 0.001:  # Small threshold for floating point errors
                        assets_with_changed_weights.add(asset)

                # For portfolio drift rebalancing, force rebalancing even if assets haven't changed
                if portfolio_needs_rebalancing and not assets_to_sell and not assets_to_buy:
                    # All assets are the same but weights need adjustment
                    assets_with_changed_weights = set(new_holdings.keys())

                # Calculate transaction costs including weight changes
                if assets_to_sell or assets_to_buy or assets_with_changed_weights:
                    # Log asset change with execution timing details
                    date_str = today.strftime('%Y-%m-%d')
                    prev_date_str = yesterday.strftime('%Y-%m-%d')

                    logging.info(f"ASSET CHANGE DETECTED on {date_str}:")
                    logging.info(f"   Signal Date: {prev_date_str} (generated at 00:00 UTC)")

                    if self.execution_timing == 'candle_close':
                        logging.info(f"   AUTOMATIC EXECUTION: {date_str} 00:00 UTC (immediate)")
                        logging.info(f"   Execution Delay: 0 hours")
                    elif self.execution_timing == 'manual_12pm':
                        logging.info(f"   MANUAL EXECUTION: {date_str} 12:00 PM UTC")
                        logging.info(f"   Execution Delay: 12 hours after signal")

                    if assets_to_sell:
                        logging.info(f"   Selling: {list(assets_to_sell)}")
                    if assets_to_buy:
                        logging.info(f"   Buying: {list(assets_to_buy)}")
                    if assets_with_changed_weights:
                        logging.info(f"   Weight Changes: {list(assets_with_changed_weights)}")

                    # Show execution prices for verification
                    for asset in assets_to_buy:
                        if asset in self.data_dict:
                            if self.execution_timing == 'candle_close':
                                price = self.data_dict[asset].loc[today, 'close']
                                logging.info(f"   {asset} buy price: ${price:.4f} (close price)")
                            elif self.execution_timing == 'manual_12pm':
                                # Get actual 12h close price for display
                                ohlc = self.data_dict[asset].loc[today]
                                close_price = ohlc['close']

                                # Get the actual 12h close price if available
                                actual_12h_price = self._get_actual_12h_close_price(asset, today)

                                if actual_12h_price is not None:
                                    # Use actual 12h close price
                                    price_diff = actual_12h_price - close_price
                                    price_diff_pct = (price_diff / close_price) * 100
                                    logging.info(f"   {asset} buy price: ${actual_12h_price:.4f} (12PM actual)")
                                    logging.info(f"      vs close price: ${close_price:.4f} (diff: {price_diff:+.4f}, {price_diff_pct:+.2f}%)")
                                else:
                                    # Fall back to approximation if 12h data not available
                                    midday_price = (ohlc['open'] + ohlc['high'] + ohlc['low'] + ohlc['close']) / 4
                                    price_diff = midday_price - close_price
                                    price_diff_pct = (price_diff / close_price) * 100
                                    logging.info(f"   {asset} buy price: ${midday_price:.4f} (12PM approx - no 12h data)")
                                    logging.info(f"      vs close price: ${close_price:.4f} (diff: {price_diff:+.4f}, {price_diff_pct:+.2f}%)")

                    # Apply transaction fee for each asset sold, bought, or with changed weights
                    transaction_cost = (len(assets_to_sell) + len(assets_to_buy) + len(assets_with_changed_weights)) * self.transaction_fee_rate
            else:
                # For equal allocation, just calculate costs for assets added or removed
                if assets_to_sell or assets_to_buy:
                    # Log asset change with execution timing details
                    date_str = today.strftime('%Y-%m-%d')
                    prev_date_str = yesterday.strftime('%Y-%m-%d')

                    logging.info(f"ASSET CHANGE DETECTED on {date_str}:")
                    logging.info(f"   Signal Date: {prev_date_str} (generated at 00:00 UTC)")

                    if self.execution_timing == 'candle_close':
                        logging.info(f"   AUTOMATIC EXECUTION: {date_str} 00:00 UTC (immediate)")
                        logging.info(f"   Execution Delay: 0 hours")
                    elif self.execution_timing == 'manual_12pm':
                        logging.info(f"   MANUAL EXECUTION: {date_str} 12:00 PM UTC")
                        logging.info(f"   Execution Delay: 12 hours after signal")

                    if assets_to_sell:
                        logging.info(f"   Selling: {list(assets_to_sell)}")
                    if assets_to_buy:
                        logging.info(f"   Buying: {list(assets_to_buy)}")

                    # Show execution prices for verification
                    for asset in assets_to_buy:
                        if asset in self.data_dict:
                            if self.execution_timing == 'candle_close':
                                price = self.data_dict[asset].loc[today, 'close']
                                logging.info(f"   {asset} buy price: ${price:.4f} (close price)")
                            elif self.execution_timing == 'manual_12pm':
                                # Get actual 12h close price for display
                                ohlc = self.data_dict[asset].loc[today]
                                close_price = ohlc['close']

                                # Get the actual 12h close price if available
                                actual_12h_price = self._get_actual_12h_close_price(asset, today)

                                if actual_12h_price is not None:
                                    # Use actual 12h close price
                                    price_diff = actual_12h_price - close_price
                                    price_diff_pct = (price_diff / close_price) * 100
                                    logging.info(f"   {asset} buy price: ${actual_12h_price:.4f} (12PM actual)")
                                    logging.info(f"      vs close price: ${close_price:.4f} (diff: {price_diff:+.4f}, {price_diff_pct:+.2f}%)")
                                else:
                                    # Fall back to approximation if 12h data not available
                                    midday_price = (ohlc['open'] + ohlc['high'] + ohlc['low'] + ohlc['close']) / 4
                                    price_diff = midday_price - close_price
                                    price_diff_pct = (price_diff / close_price) * 100
                                    logging.info(f"   {asset} buy price: ${midday_price:.4f} (12PM approx - no 12h data)")
                                    logging.info(f"      vs close price: ${close_price:.4f} (diff: {price_diff:+.4f}, {price_diff_pct:+.2f}%)")

                    # Apply transaction fee for each asset sold and bought
                    transaction_cost = (len(assets_to_sell) + len(assets_to_buy)) * self.transaction_fee_rate

            # Calculate portfolio return for the current day
            # If MTPI signal is bearish, we've already exited positions at the beginning of the day
            # so we don't experience any market movement
            portfolio_return = 0.0
            asset_returns_today = {}

            # Calculate returns if we're staying in the market OR entering the market
            if mtpi_allows_trade:
                if self.use_weighted_allocation:
                    # Weighted allocation
                    if current_holdings:
                        # Calculate weighted return from current holdings
                        for asset, weight in current_holdings.items():
                            if asset in asset_returns:
                                # The return at 'today' represents the percentage change from the previous candle's close to the current candle's close
                                # This matches TradingView's approach to calculating returns
                                asset_return = asset_returns[asset].loc[today]
                                portfolio_return += asset_return * weight
                                asset_returns_today[asset] = asset_return
                    elif new_holdings:
                        # We're entering the market today - calculate returns for new holdings
                        for asset, weight in new_holdings.items():
                            if asset in asset_returns:
                                # The return at 'today' represents the percentage change from the previous candle's close to the current candle's close
                                # This matches TradingView's approach to calculating returns
                                asset_return = asset_returns[asset].loc[today]
                                portfolio_return += asset_return * weight
                                asset_returns_today[asset] = asset_return
                else:
                    # Equal allocation
                    if current_holdings:
                        # Calculate equal-weighted return from current holdings
                        # IMPORTANT: We use the return at the current timestamp, which represents
                        # the price movement from the previous candle, not the current one
                        num_assets = len(current_holdings)
                        for asset in current_holdings:
                            if asset in asset_returns:
                                # The return at 'today' represents the percentage change from the previous candle's close to the current candle's close
                                # This matches TradingView's approach to calculating returns
                                asset_return = asset_returns[asset].loc[today]
                                portfolio_return += asset_return / num_assets
                                asset_returns_today[asset] = asset_return
                    elif new_holdings:
                        # We're entering the market today - calculate returns for new holdings
                        # This ensures we experience market movement on entry days
                        # The return at the current timestamp represents the price movement from the previous candle
                        num_assets = len(new_holdings)
                        for asset in new_holdings:
                            if asset in asset_returns:
                                # The return at 'today' represents the percentage change from the previous candle's close to the current candle's close
                                # This matches TradingView's approach to calculating returns
                                asset_return = asset_returns[asset].loc[today]
                                portfolio_return += asset_return / num_assets
                                asset_returns_today[asset] = asset_return

            # Calculate new equity value
            if i == start_idx + 1:
                # First day calculation
                if mtpi_allows_trade and new_holdings:
                    # Apply transaction fee for initial purchases
                    fee_factor = 1 - (len(new_holdings) * self.transaction_fee_rate)

                    # Apply both transaction fee and market movement for the day
                    equity_curve.iloc[i] = equity_curve.iloc[i-1] * fee_factor * (1 + portfolio_return)
                    current_holdings = new_holdings

                    # Record the assets held
                    if self.use_weighted_allocation:
                        for asset, weight in current_holdings.items():
                            assets_held.loc[today, asset] = weight
                    else:
                        for asset in current_holdings:
                            assets_held.loc[today, asset] = 1.0 / len(current_holdings)
                else:
                    equity_curve.iloc[i] = equity_curve.iloc[i-1]
            else:
                # Subsequent days
                if mtpi_allows_trade:
                    # Apply portfolio return and transaction costs
                    equity_value = equity_curve.iloc[i-1] * (1 + portfolio_return)

                    # Apply transaction costs if there are changes in holdings or rebalancing
                    if assets_to_sell or assets_to_buy or portfolio_needs_rebalancing:
                        equity_value *= (1 - transaction_cost)

                        # Print confirmation when rebalancing actually occurs
                        if portfolio_needs_rebalancing:
                            print(f"REBALANCING EXECUTED - Transaction cost: {transaction_cost:.3%}")
                            print(f"Portfolio value after rebalancing: ${equity_value:,.2f}")

                    equity_curve.iloc[i] = equity_value

                    # Update holdings
                    current_holdings = new_holdings

                    # Record the assets held
                    assets_held.loc[today] = 0.0  # Reset allocations
                    if self.use_weighted_allocation:
                        for asset, weight in current_holdings.items():
                            assets_held.loc[today, asset] = weight
                    else:
                        for asset in current_holdings:
                            assets_held.loc[today, asset] = 1.0 / len(current_holdings) if current_holdings else 0.0
                else:
                    # If MTPI signal is not bullish, exit positions immediately at the beginning of the day
                    # and stay in cash - we don't experience any market movement for this day
                    if current_holdings:  # Only apply transaction fee if we have holdings to sell
                        # Since we exit at the beginning of the day, we don't apply portfolio return
                        # We only apply transaction fees for selling all current holdings
                        transaction_cost = len(current_holdings) * self.transaction_fee_rate
                        equity_value = equity_curve.iloc[i-1] * (1 - transaction_cost)
                        equity_curve.iloc[i] = equity_value


                    else:
                        # No holdings to sell, just keep the same equity value
                        equity_curve.iloc[i] = equity_curve.iloc[i-1]

                        # Only set transaction cost to 0 if we're not already out of the market
                        # This ensures we don't show transaction costs for periods where we're already out
                        if i > start_idx + 1:  # Not the first day
                            prev_allocation = self.allocation_history[-1] if self.allocation_history else {}
                            prev_holdings = prev_allocation.get('current_holdings', [])
                            if prev_holdings:  # We had holdings in the previous period
                                transaction_cost = 0.0  # We're exiting the market, so there's no transaction cost

                    current_holdings = {}  # Empty dictionary instead of set
                    assets_held.loc[today] = 0.0  # Reset allocations

            # Record allocation and return history for analysis
            allocation_entry = {
                'date': today,
                'mtpi_signal': mtpi_signal_value,
                'top_assets': top_assets,
                'current_holdings': list(current_holdings.keys()) if current_holdings else [],
                'portfolio_return': portfolio_return,
                'transaction_cost': transaction_cost,
                'equity_value': equity_curve.iloc[i],
                'rebalancing_enabled': self.enable_rebalancing,
                'rebalance_threshold': self.rebalance_threshold,
                'rebalanced': portfolio_needs_rebalancing,
                'rebalancing_reason': rebalancing_reason if portfolio_needs_rebalancing else "",
                'scores': filtered_scores  # Add the scores used for asset selection
            }

            # Add weights information for debugging weighted allocation
            if self.use_weighted_allocation and current_holdings:
                allocation_entry['weights'] = {asset: weight for asset, weight in current_holdings.items()}

            self.allocation_history.append(allocation_entry)

            # Record individual asset returns for this timestamp
            asset_returns_record = {'date': today}
            for asset in self.selected_assets:
                if asset in asset_returns and today in asset_returns[asset].index:
                    asset_returns_record[asset] = asset_returns[asset].loc[today]
            self.return_history.append(asset_returns_record)

        # Store results
        self.strategy_equity_curve = equity_curve
        self.assets_held_df = assets_held

        # Create a series of held assets for trade tracking
        best_asset_series = pd.Series('', index=assets_held.index)

        # For each date, determine the held asset(s) or empty string if out of market
        for date in assets_held.index:
            held_assets = assets_held.loc[date]
            held_asset_list = held_assets[held_assets > 0].index.tolist()

            if held_asset_list:
                if len(held_asset_list) == 1:
                    # Single asset case
                    best_asset_series.loc[date] = held_asset_list[0]
                else:
                    # Multiple assets case - use a comma-separated string
                    best_asset_series.loc[date] = ','.join(held_asset_list)
            else:
                # Out of market
                best_asset_series.loc[date] = ''

        # Calculate performance metrics for strategy
        self.strategy_metrics = calculate_all_metrics(
            equity_curve.dropna(),
            best_asset_series=best_asset_series.loc[equity_curve.dropna().index],  # Pass the asset series for trade calculation
            risk_free_rate=0.0
        )

        # Trade metrics are stored in self.strategy_metrics for use in plotting

        # Calculate metrics for each B&H curve
        self.bnh_metrics = {}

        # Calculate accumulated returns for strategy and B&H
        self.accumulated_returns = {}

        # Calculate strategy total return
        if not equity_curve.dropna().empty:
            start_value = equity_curve.dropna().iloc[0]
            end_value = equity_curve.dropna().iloc[-1]
            total_return_pct = ((end_value / start_value) - 1) * 100
            self.accumulated_returns['Strategy'] = total_return_pct
        else:
            self.accumulated_returns['Strategy'] = None

        # End timing
        self.end_time = time.time()
        self.elapsed_time = self.end_time - self.start_time
        logging.info(f"Strategy execution completed in {format_elapsed_time(self.elapsed_time)}")
        logging.info(f"DEBUG: self.elapsed_time = {self.elapsed_time} seconds")

        return equity_curve, assets_held, mtpi_filtering_stats

    def _fetch_12h_candle_data(self, symbol: str) -> pd.DataFrame:
        """
        Fetch 12-hour candle data for manual execution timing.

        Args:
            symbol: Asset symbol

        Returns:
            DataFrame with 12h OHLCV data
        """
        try:
            from src.data_fetcher import fetch_ohlcv_data

            # Use the same effective start date as the main strategy (including warmup)
            # Calculate the effective start date with warmup
            analysis_start = pd.Timestamp(self.analysis_start_date, tz='UTC')
            warmup_days = 60  # Same as used in main strategy
            effective_start_date = analysis_start - pd.Timedelta(days=warmup_days)
            start_date = effective_start_date.strftime('%Y-%m-%d')
            end_date = None  # Fetch until current date

            logging.info(f"Fetching 12h candle data for {symbol} for manual execution timing...")

            # Fetch 12h candles
            twelve_hour_data_dict = fetch_ohlcv_data(
                exchange_id='binance',
                symbols=[symbol],
                timeframe='12h',
                since=start_date,
                use_cache=self.use_cache
            )

            # Extract the DataFrame for this symbol
            twelve_hour_data = twelve_hour_data_dict.get(symbol, pd.DataFrame())

            if twelve_hour_data.empty:
                logging.warning(f"No 12h data available for {symbol}, falling back to approximation")
                return pd.DataFrame()

            logging.info(f"Fetched {len(twelve_hour_data)} 12h candles for {symbol}")
            return twelve_hour_data

        except Exception as e:
            logging.error(f"Failed to fetch 12h data for {symbol}: {e}")
            return pd.DataFrame()

    def _calculate_manual_execution_returns(self, symbol: str, df: pd.DataFrame) -> pd.Series:
        """
        Calculate returns for manual execution at 12 PM UTC.

        For daily timeframe, this fetches 12h candle data to get actual 12 PM UTC close prices.

        Args:
            symbol: Asset symbol
            df: DataFrame with daily OHLCV data

        Returns:
            Series of returns for manual execution timing
        """
        if self.timeframe != '1d':
            # For non-daily timeframes, fall back to standard calculation
            logging.warning(f"Manual execution timing only supported for daily timeframe, using standard calculation for {symbol}")
            return df['close'].pct_change().fillna(0.0)

        # Fetch 12h candle data to get actual 12 PM UTC prices
        twelve_hour_data = self._fetch_12h_candle_data(symbol)

        if twelve_hour_data.empty:
            # Fall back to approximation if 12h data is not available
            logging.warning(f"Using price approximation for {symbol} manual execution")
            midday_price_approx = (df['open'] + df['high'] + df['low'] + df['close']) / 4

            returns = pd.Series(index=df.index, dtype=float)
            for i in range(1, len(df)):
                prev_close = df['close'].iloc[i-1]
                current_midday = midday_price_approx.iloc[i]
                returns.iloc[i] = (current_midday - prev_close) / prev_close
            returns.iloc[0] = 0.0
            return returns.fillna(0.0)

        # Filter 12h data to get midnight candles (00:00 AM candles that close at 12:00 PM)
        # These candles contain the actual 12:00 PM close price we need for manual execution
        midnight_candles = twelve_hour_data[twelve_hour_data.index.hour == 0]

        if midnight_candles.empty:
            logging.warning(f"No midnight candles found in 12h data for {symbol}, using approximation")
            # Fall back to approximation
            midday_price_approx = (df['open'] + df['high'] + df['low'] + df['close']) / 4
            returns = pd.Series(index=df.index, dtype=float)
            for i in range(1, len(df)):
                prev_close = df['close'].iloc[i-1]
                current_midday = midday_price_approx.iloc[i]
                returns.iloc[i] = (current_midday - prev_close) / prev_close
            returns.iloc[0] = 0.0
            return returns.fillna(0.0)

        # Calculate returns from previous daily close to 12 PM close
        returns = pd.Series(index=df.index, dtype=float)

        # Log detailed execution timing for verification
        logging.info(f"MANUAL EXECUTION TIMING VERIFICATION for {symbol}:")
        logging.info(f"   Execution Method: Manual at 12 PM UTC (using midnight candle close)")
        logging.info(f"   Data Source: 12-hour candles")
        logging.info(f"   Available midnight candles: {len(midnight_candles)}")

        execution_log_count = 0
        for i in range(1, len(df)):
            date = df.index[i]
            prev_date = df.index[i-1]
            prev_close = df['close'].iloc[i-1]

            # Find the midnight candle for this date (starts at 00:00, closes at 12:00 PM)
            midnight_date = date.replace(hour=0, minute=0, second=0, microsecond=0)

            if midnight_date in midnight_candles.index:
                # Use actual 12 PM close price (from midnight candle close)
                twelve_pm_close = midnight_candles.loc[midnight_date, 'close']
                returns.iloc[i] = (twelve_pm_close - prev_close) / prev_close

                # Log first few executions for verification
                if execution_log_count < 5:
                    date_str = date.strftime('%Y-%m-%d')
                    prev_date_str = prev_date.strftime('%Y-%m-%d')
                    logging.info(f"   {date_str}: Signal generated at {prev_date_str} 00:00 UTC close (${prev_close:.4f})")
                    logging.info(f"      Executed at {date_str} 12:00 PM UTC actual (${twelve_pm_close:.4f})")
                    logging.info(f"      Return: {returns.iloc[i]:.4f} ({returns.iloc[i]*100:.2f}%)")
                    logging.info(f"      Execution delay: 12 hours after signal")
                    execution_log_count += 1
            else:
                # If no midnight candle available for this date, use approximation
                ohlc = df.iloc[i]
                midday_approx = (ohlc['open'] + ohlc['high'] + ohlc['low'] + ohlc['close']) / 4
                returns.iloc[i] = (midday_approx - prev_close) / prev_close

                if execution_log_count < 5:
                    date_str = date.strftime('%Y-%m-%d')
                    prev_date_str = prev_date.strftime('%Y-%m-%d')
                    logging.info(f"   {date_str}: Signal generated at {prev_date_str} 00:00 UTC close (${prev_close:.4f})")
                    logging.info(f"      Executed at {date_str} 12:00 PM UTC approx (${midday_approx:.4f}) [fallback]")
                    logging.info(f"      Return: {returns.iloc[i]:.4f} ({returns.iloc[i]*100:.2f}%)")
                    execution_log_count += 1

        # Fill first value with 0
        returns.iloc[0] = 0.0

        logging.info(f"Manual execution returns calculated for {symbol} using actual 12h candle data")
        return returns.fillna(0.0)

    def _get_actual_12h_close_price(self, symbol: str, date: pd.Timestamp) -> Optional[float]:
        """
        Get the actual 12h close price for a given symbol and date.

        Args:
            symbol: Asset symbol
            date: Date for which to get the 12h close price

        Returns:
            The actual 12h close price if available, None otherwise
        """
        try:
            # Check if we have 12h data cached for this symbol
            if not hasattr(self, '_twelve_hour_data_cache'):
                self._twelve_hour_data_cache = {}

            # Fetch 12h data if not cached
            if symbol not in self._twelve_hour_data_cache:
                twelve_hour_data = self._fetch_12h_candle_data(symbol)
                if twelve_hour_data.empty:
                    self._twelve_hour_data_cache[symbol] = None
                    return None

                # Filter to get midnight candles (00:00 AM candles that close at 12:00 PM)
                midnight_candles = twelve_hour_data[twelve_hour_data.index.hour == 0]
                self._twelve_hour_data_cache[symbol] = midnight_candles

            midnight_candles = self._twelve_hour_data_cache[symbol]
            if midnight_candles is None or midnight_candles.empty:
                return None

            # Find the midnight candle for this date (starts at 00:00, closes at 12:00 PM)
            midnight_date = date.replace(hour=0, minute=0, second=0, microsecond=0)

            if midnight_date in midnight_candles.index:
                return midnight_candles.loc[midnight_date, 'close']
            else:
                return None

        except Exception as e:
            logging.warning(f"Failed to get 12h close price for {symbol} on {date}: {e}")
            return None

    def save_allocation_history(self, filename=None):
        """Save allocation history to a CSV file for debugging."""
        if not self.allocation_history:
            logging.error("No allocation history available. Run the strategy first.")
            return

        # Create a DataFrame from the allocation history
        allocation_df = pd.DataFrame(self.allocation_history)

        # Handle the weights column which contains dictionaries
        if 'weights' in allocation_df.columns:
            # Convert the weights dictionaries to strings for CSV output
            allocation_df['weights'] = allocation_df['weights'].apply(lambda x: str(x) if x else '')

        # Generate filename if not provided
        if filename is None:
            # Determine MTPI status
            mtpi_status = "with_mtpi" if self.use_mtpi else "no_mtpi"

            # Determine rebalancing status
            rebalance_status = f"rebal_{int(self.rebalance_threshold*100)}pct" if self.enable_rebalancing else "no_rebal"

            # Create a descriptive filename with all parameters
            if self.use_weighted_allocation:
                weights_str = "-".join([f"{w*100:.0f}" for w in self.weights])
                filename = f'allocation_history_weighted_{weights_str}_{self.timeframe}_{self.mtpi_timeframe}_{mtpi_status}_{rebalance_status}_{self.analysis_start_date}.csv'
            else:
                filename = f'allocation_history_{self.timeframe}_{self.mtpi_timeframe}_{mtpi_status}_{rebalance_status}_{self.analysis_start_date}.csv'

        # Save to CSV
        allocation_df.to_csv(filename)
        logging.info(f"Saved allocation history to {filename}")

        return allocation_df

    def analyze_allocation(self):
        """Analyze allocation decisions and returns."""
        if not self.allocation_history:
            logging.error("No allocation history available. Run the strategy first.")
            return

        # Convert allocation history to DataFrame for analysis
        allocation_df = pd.DataFrame(self.allocation_history)
        return_df = pd.DataFrame(self.return_history)

        # Set date as index
        allocation_df.set_index('date', inplace=True)
        return_df.set_index('date', inplace=True)

        # Save allocation history to CSV for debugging
        self.save_allocation_history()

        # Calculate cumulative returns for each asset
        cumulative_returns = {}

        # Get the start date of the strategy equity curve (after NaN values are dropped)
        strategy_start_date = self.strategy_equity_curve.dropna().index[0]
        logging.info(f"Strategy equity curve starts at: {strategy_start_date.date()}")

        # Log the assets that will be included in the buy-and-hold comparison
        logging.info(f"Assets included in buy-and-hold comparison:")
        for asset in self.selected_assets:
            if asset in self.data_dict:
                asset_first_date = self.data_dict[asset].index.min()
                logging.info(f"  {asset}: First available date {asset_first_date.date()}")
                if asset_first_date > strategy_start_date:
                    logging.info(f"    Note: {asset} starts later than strategy start date")
            else:
                logging.warning(f"  {asset}: Not found in data_dict")

        # Calculate buy-and-hold curves using the improved function that handles newer assets
        from src.strategy import calculate_buy_and_hold_curves

        # Convert analysis_end_date to timestamp if provided
        analysis_end_timestamp = None
        if self.analysis_end_date:
            analysis_end_timestamp = pd.Timestamp(self.analysis_end_date, tz='UTC')

        cumulative_returns = calculate_buy_and_hold_curves(
            data_dict=self.data_dict,
            initial_capital=self.initial_capital,
            normalize_to_strategy_start=True,
            strategy_start_date=strategy_start_date,
            analysis_end_date=analysis_end_timestamp,
            transaction_fee_rate=0.001  # 0.1% transaction fee
        )

        # Calculate metrics for each buy-and-hold curve
        for asset in self.selected_assets:
            if asset in cumulative_returns:
                # Filter the buy-and-hold curve to the analysis date range if end date is specified
                bnh_curve = cumulative_returns[asset]
                if analysis_end_timestamp is not None:
                    # Filter to only include data up to the analysis end date
                    bnh_curve = bnh_curve[bnh_curve.index <= analysis_end_timestamp]
                    logging.info(f"Filtered {asset} B&H curve to end at {analysis_end_timestamp.date()}")

                # Calculate metrics for this asset's buy-and-hold curve
                self.bnh_metrics[asset] = calculate_all_metrics(bnh_curve)

                # Ensure the equity curve is stored in the metrics
                self.bnh_metrics[asset]['equity_curve'] = bnh_curve
                logging.info(f"Added equity_curve to bnh_metrics for {asset} with {len(bnh_curve)} points")

                # Fix total return calculation for assets with NaN values
                equity_curve = cumulative_returns[asset]
                valid_equity = equity_curve.dropna()

                if not valid_equity.empty and len(valid_equity) >= 2:
                    start_value = valid_equity.iloc[0]
                    end_value = valid_equity.iloc[-1]
                    if start_value > 0:  # Avoid division by zero
                        total_return_pct = ((end_value / start_value) - 1) * 100
                        self.accumulated_returns[f'{asset} B&H'] = total_return_pct
                        logging.info(f"{asset} B&H total return: {total_return_pct:.2f}%")
                    else:
                        self.accumulated_returns[f'{asset} B&H'] = None
                        logging.warning(f"{asset} B&H curve starts with zero value")
                else:
                    self.accumulated_returns[f'{asset} B&H'] = None
                    logging.warning(f"{asset} B&H curve has insufficient valid data")
            else:
                logging.warning(f"No buy-and-hold curve calculated for {asset}")
                self.accumulated_returns[f'{asset} B&H'] = None

        # Plot allocation and returns with performance metrics
        self.plot_results(allocation_df, return_df, cumulative_returns)

        return allocation_df, return_df

    def align_dataframes(self, data_dict, timeframe='1d'):
        """
        Align DataFrames to a continuous datetime index while tracking each asset's first available date.
        This ensures assets are only included in the strategy after they actually exist.
        """
        if not data_dict:
            return {}

        # Find the global min and max dates
        global_min_date = None
        global_max_date = None

        # Track each asset's first available date
        self.asset_first_dates = {}

        for symbol, df in data_dict.items():
            if df.empty:
                continue

            # Store the first date for each asset
            asset_min_date = df.index.min()
            asset_max_date = df.index.max()

            # Special handling for AUTISM token - set first available date to July 19, 2024
            if symbol == 'AUTISM/USDT':
                autism_start_date = pd.Timestamp('2024-07-19', tz='UTC')
                if asset_min_date < autism_start_date:
                    logging.warning(f"Setting AUTISM/USDT first available date to 2024-07-19 instead of {asset_min_date.date()} to avoid skewing strategy results")
                    asset_min_date = autism_start_date

            # Save the first date this asset is available
            self.asset_first_dates[symbol] = asset_min_date
            logging.info(f"Asset {symbol} first available date: {asset_min_date}")

            if global_min_date is None or asset_min_date < global_min_date:
                global_min_date = asset_min_date

            if global_max_date is None or asset_max_date > global_max_date:
                global_max_date = asset_max_date

        if global_min_date is None or global_max_date is None:
            logging.warning("No valid dates found in any dataframes.")
            return {}

        # Map timeframe to pandas frequency
        timeframe_to_freq = {
            '1m': 'min', '5m': '5min', '15m': '15min', '30m': '30min',
            '1h': 'H', '2h': '2H', '3h': '3H', '4h': '4H', '6h': '6H', '8h': '8H', '12h': '12H',
            '1d': 'D', '3d': '3D', '1w': 'W', '1M': 'M'
        }

        # Get the frequency
        freq = timeframe_to_freq.get(timeframe, 'D')

        # Create a continuous date range
        continuous_index = pd.date_range(start=global_min_date, end=global_max_date, freq=freq)

        # Reindex each DataFrame
        aligned_data = {}
        for symbol, df in data_dict.items():
            if df.empty:
                continue

            # Get the first date for this asset
            first_date = self.asset_first_dates[symbol]

            # Create a mask for dates when this asset exists
            exists_mask = continuous_index >= first_date

            # Reindex to the continuous index but only fill values after the asset exists
            reindexed_df = pd.DataFrame(index=continuous_index, columns=df.columns)

            # Only copy data for dates when the asset exists
            valid_dates = continuous_index[exists_mask]
            if not valid_dates.empty:
                # Get the intersection of valid_dates and df.index
                common_dates = df.index.intersection(valid_dates)

                # Copy data for common dates
                reindexed_df.loc[common_dates] = df.loc[common_dates]

                # Forward fill only after the first date
                reindexed_df = reindexed_df.ffill()
                # Ensure proper data types after forward fill
                reindexed_df = reindexed_df.infer_objects(copy=False)

            aligned_data[symbol] = reindexed_df

        return aligned_data

    def plot_results(self, allocation_df, return_df, cumulative_returns):
        """Plot allocation decisions and returns."""
        # Close any existing figures to prevent warnings
        plt.close('all')

        # Set style to match TradingView dark theme
        plt.style.use('dark_background')

        # Create figure with dimensions optimized for horizontal layout (chart on left, table on right)
        # Using a wider aspect ratio to accommodate both chart and table side by side
        plt.figure(figsize=(24, 12), dpi=100)  # Even wider and taller figure for better table display

        # Adjust margins to make room for the legend and provide spacing between chart and table
        plt.subplots_adjust(bottom=0.2, wspace=0.3)

        # Use elapsed time for display in the plot

        # Get trend method from config
        from src.config_manager import load_config
        config = load_config()
        trend_method = config.get('settings', {}).get('trend_method', 'RSI')

        # Use total_elapsed_time if available, otherwise fall back to elapsed_time
        display_time = self.total_elapsed_time if self.total_elapsed_time is not None else self.elapsed_time

        # Add strategy information at the top of the chart
        mtpi_signal_status = "Bullish (1)" if self.mtpi_signals.iloc[-1] == 1 else "Bearish (0)"

        # Build date range string
        date_range = f"Starting {self.analysis_start_date}"
        if self.analysis_end_date:
            date_range += f" to {self.analysis_end_date}"

        strategy_info = f"Asset Rotation Strategy Analysis (Data: {self.timeframe}, MTPI: {self.mtpi_timeframe}) - {date_range} | MTPI Signal: {mtpi_signal_status} | Elapsed: {format_elapsed_time(display_time)}"

        # Create a text box with background for better visibility
        plt.figtext(0.5, 0.97, strategy_info,
                   ha='center',
                   fontsize=12,
                   color='white',
                   weight='bold',
                   bbox=dict(facecolor='black', alpha=0.7, edgecolor='gray', boxstyle='round,pad=0.5'))

        # Define asset colors to match TradingView with expanded color palette
        asset_colors = {
            # Keep existing colors for major assets
            'BTC/USDT': '#3373b3',  # Blue
            'ETH/USDT': '#e84142',  # Red
            'SOL/USDT': '#14f195',  # Green
            'SUI/USDT': '#ff9332',  # Orange
            'AVAX/USDT': '#e84142',  # Red
            'NEAR/USDT': '#000000',  # Black
            'INJ/USDT': '#18d496',   # Teal
            'OP/USDT': '#ff0420',    # Bright Red
            'ARB/USDT': '#28a0f0',   # Light Blue
            'MATIC/USDT': '#8247e5',  # Purple

            # Additional predefined colors for other assets (40+ total)
            'LINK/USDT': '#2a5ada',  # Chainlink blue
            'DOT/USDT': '#e6007a',   # Polkadot pink
            'ADA/USDT': '#0033ad',   # Cardano blue
            'DOGE/USDT': '#c3a634',  # Dogecoin gold
            'SHIB/USDT': '#faa21a',  # Shiba Inu orange
            'UNI/USDT': '#ff007a',   # Uniswap pink
            'ATOM/USDT': '#2e3148',  # Cosmos dark blue
            'LTC/USDT': '#345d9d',   # Litecoin blue
            'AAVE/USDT': '#b6509e',  # AAVE pink
            'ALGO/USDT': '#000000',  # Algorand black
            'APE/USDT': '#0052ff',   # ApeCoin blue
            'APT/USDT': '#09d8c4',   # Aptos teal
            'BCH/USDT': '#8dc351',   # Bitcoin Cash green
            'BNB/USDT': '#f3ba2f',   # Binance yellow
            'COMP/USDT': '#00d395',  # Compound green
            'CRO/USDT': '#103f68',   # Cronos blue
            'DAI/USDT': '#f5ac37',   # DAI gold
            'DASH/USDT': '#008ce7',  # Dash blue
            'EOS/USDT': '#333333',   # EOS dark gray
            'ETC/USDT': '#328332',   # Ethereum Classic green
            'FIL/USDT': '#0090ff',   # Filecoin blue
            'FTM/USDT': '#1969ff',   # Fantom blue
            'GALA/USDT': '#00d8ff',  # Gala cyan
            'HBAR/USDT': '#222222',  # Hedera dark gray
            'ICP/USDT': '#3b00b9',   # Internet Computer purple
            'MANA/USDT': '#ff2d55',  # Decentraland red
            'SAND/USDT': '#00aeff',  # The Sandbox blue
            'THETA/USDT': '#2ab8e6',  # Theta blue
            'TRX/USDT': '#ff0013',   # TRON red
            'VET/USDT': '#15bdff',   # VeChain blue
            'XLM/USDT': '#14b6e7',   # Stellar blue
            'XMR/USDT': '#ff6600',   # Monero orange
            'XTZ/USDT': '#2c7df7',   # Tezos blue
            'ZEC/USDT': '#ecb244',   # Zcash gold

            # Memecoins and special tokens
            'AUTISM/USDT': '#9370DB',  # Medium purple
            'BONK/USDT': '#FFD700',    # Gold
            'SAMO/USDT': '#FF5733',    # Orange-red
            'PEPE/USDT': '#32CD32',    # Lime green
            'FWOG/USDT': '#00FF7F',    # Spring green
            'TOSHI/USDT': '#4B0082',   # Indigo
            'ALTURA/USDT': '#1E90FF'   # Dodger blue
        }

        # We no longer need default colors as we're using deterministic color generation

        # Function to generate a deterministic color for assets not in the predefined list
        def generate_asset_color(asset):
            """Generate a deterministic color based on the asset name."""
            import hashlib

            # Create a hash of the asset name
            hash_object = hashlib.md5(asset.encode())
            hash_hex = hash_object.hexdigest()

            # Use the first 6 characters of the hash as the color
            color = '#' + hash_hex[:6]

            return color

        # Use GridSpec for more control over subplot sizes - changed to horizontal layout (chart on left, table on right)
        # Adjusted width ratios to give more space to the table
        gs = gridspec.GridSpec(1, 2, width_ratios=[3, 2])  # Chart takes 3/5 of width, table takes 2/5

        # Plot 1: Strategy equity curve with logarithmic y-axis (left plot)
        ax1 = plt.subplot(gs[0, 0])

        # Different plotting approach for single-asset vs multi-asset strategy
        if self.n_assets == 1:
            # For single-asset strategy, use colored segments based on which asset is held
            logging.info("Using colored segments for single-asset strategy visualization")

            # Get the filtered equity curve
            filtered_equity = self.strategy_equity_curve.dropna()

            # Find segments of consecutive days with the same asset
            segments = []
            current_asset = None
            start_idx = 0

            # Extract the held asset for each day
            for i, date in enumerate(filtered_equity.index):
                # Find which asset is held on this day (if any)
                held_assets = self.assets_held_df.loc[date]
                held_asset = held_assets[held_assets > 0].index.tolist()

                if held_asset:
                    asset = held_asset[0]  # Take the first (and only) asset if held
                else:
                    asset = ''  # Empty string for out of market

                # Check if this is a new segment
                if asset != current_asset:
                    # End previous segment if not the first point
                    if i > 0:
                        segments.append({
                            'asset': current_asset,
                            'start_idx': start_idx,
                            'end_idx': i
                        })

                    # Start new segment
                    current_asset = asset
                    start_idx = i

            # Add the final segment
            segments.append({
                'asset': current_asset,
                'start_idx': start_idx,
                'end_idx': len(filtered_equity)
            })

            # Plot each segment with its own color
            plotted_labels = set()  # To avoid duplicate legend entries

            for segment in segments:
                asset = segment['asset']
                start_idx = segment['start_idx']
                end_idx = segment['end_idx']

                # Get the date range and equity values for this segment
                segment_dates = filtered_equity.index[start_idx:end_idx]
                segment_equity = filtered_equity.iloc[start_idx:end_idx]

                # Skip if segment is empty
                if segment_equity.empty:
                    continue

                # For clean transitions, ensure segments connect perfectly
                # If this is not the first segment, add the last point from previous segment
                if start_idx > 0:
                    prev_date = filtered_equity.index[start_idx-1]
                    # Add the previous point to this segment for a clean transition
                    segment_dates = pd.DatetimeIndex([prev_date] + list(segment_dates))
                    prev_value = pd.Series([filtered_equity.iloc[start_idx-1]], index=[prev_date])
                    segment_equity = pd.concat([prev_value, segment_equity])

                # Determine color and label
                if asset == '':
                    color = 'dimgray'
                    label = 'Out of Market'
                    line_width = 2.5
                else:
                    # Use predefined color if available, otherwise generate a deterministic color
                    if asset in asset_colors:
                        color = asset_colors[asset]
                    else:
                        color = generate_asset_color(asset)
                    label = f'Holding {asset}'
                    line_width = 3.0

                # Plot the segment
                if label not in plotted_labels:
                    ax1.plot(
                        segment_dates,
                        segment_equity,
                        label=label,
                        color=color,
                        linewidth=line_width
                    )
                    plotted_labels.add(label)
                else:
                    ax1.plot(
                        segment_dates,
                        segment_equity,
                        color=color,
                        linewidth=line_width
                    )
        else:
            # For multi-asset strategy, use a single blue line
            ax1.plot(
                self.strategy_equity_curve.dropna(),
                label=f'Holding top {self.n_assets} assets',
                color='#0066cc',  # Darker blue for strategy
                linewidth=3.0
            )

        # Add buy-and-hold curves for comparison
        for asset, returns in cumulative_returns.items():
            # The returns are already normalized to start at the same initial value as the strategy
            # No need to multiply by strategy start value since we already use initial_capital
            normalized_returns = returns

            # Get color for this asset
            if asset in asset_colors:
                color = asset_colors[asset]
            else:
                # Use deterministic color generation instead of cycling through default colors
                color = generate_asset_color(asset)
                # No need to increment color_idx anymore

            ax1.plot(
                normalized_returns,
                label=f'{asset} B&H',
                color=color,
                linewidth=1.5,
                alpha=0.8
            )

        # Add grid with lower alpha for better visibility
        ax1.grid(True, alpha=0.2, linestyle='--')

        # Set logarithmic scale for y-axis
        ax1.set_yscale('log')

        # Get the current top assets from the latest data
        current_top_assets = []
        if self.assets_held_df is not None and not self.assets_held_df.empty:
            # Get the last row with non-zero allocations
            last_row = self.assets_held_df.iloc[-1]
            # Find assets with non-zero allocation
            current_top_assets = [asset for asset in last_row.index if last_row[asset] > 0]
            # Sort by allocation value (descending)
            current_top_assets.sort(key=lambda x: last_row[x], reverse=True)

        # Format the current top assets for display
        current_assets_str = ""
        if current_top_assets:
            current_assets_str = f" (Current: {', '.join(current_top_assets)})"

        # Add title and labels with better formatting
        if self.use_weighted_allocation:
            weights_str = ", ".join([f"{w*100:.1f}%" for w in self.weights])
            ax1.set_title(f'Top {self.n_assets} Assets Strategy with Weighted Allocation ({weights_str}){current_assets_str} vs Buy-and-Hold ({self.timeframe})', fontsize=14, pad=10)
        else:
            ax1.set_title(f'Top {self.n_assets} Assets Strategy with Equal Allocation{current_assets_str} vs Buy-and-Hold ({self.timeframe})', fontsize=14, pad=10)
        ax1.set_ylabel('Normalized Equity (Initial = 1.0)', fontsize=12)

        # Place legend below the chart to avoid blocking any part of the chart
        # Make it more compact with multiple columns for better readability

        # Determine a reasonable number of columns based on n_assets
        # For n_assets=1, we'll have 3 lines (strategy, B&H, out of market)
        # For n_assets=3, we'll have 7 lines (3 assets, 3 B&H, out of market)
        # For n_assets=5, we'll have 11 lines (5 assets, 5 B&H, out of market)
        total_legend_items = 2 * self.n_assets + 1  # assets + B&H + out of market

        # Use more columns for more items, with higher cap for many assets
        if total_legend_items <= 4:
            legend_ncol = 2
        elif total_legend_items <= 8:
            legend_ncol = 3
        elif total_legend_items <= 16:
            legend_ncol = 4
        elif total_legend_items <= 30:
            legend_ncol = 5
        else:
            legend_ncol = 6  # Use up to 6 columns for 40+ assets

        # Adjusted legend position for horizontal layout
        ax1.legend(loc='upper center', framealpha=0.7, fontsize=8,  # Reduced font size
                  bbox_to_anchor=(0.5, -0.15), ncol=legend_ncol, handlelength=1.2,
                  columnspacing=0.8, borderaxespad=0.8)

        # Format x-axis dates
        ax1.xaxis.set_major_formatter(plt.matplotlib.dates.DateFormatter('%Y-%m'))
        plt.xticks(rotation=45)

        # Plot 2: Performance metrics table (right plot)
        ax2 = plt.subplot(gs[0, 1])
        ax2.axis('off')  # Hide axes

        # Define the exact metrics to display in the exact order from the image
        metrics_order = [
            'Total increase',
            'Num Trades',
            'Time Between Trades',
            'Stdev',
            'Mean Pos Return',
            'Mean Neg Return',
            'Stdev Pos',
            'Stdev Neg',
            'Sharpe Ratio',
            'Sortino Ratio',
            'Omega Ratio',
            'Max Drawdown'
        ]

        # Create data dictionary for each metric
        metrics_data = {
            'Total increase': self.accumulated_returns,
            'Num Trades': {k: self.strategy_metrics.get('num_trades') if k == 'Strategy' else 1 for k in self.accumulated_returns.keys()},
            'Time Between Trades': {k: self.strategy_metrics.get('avg_time_between_trades') if k == 'Strategy' else 'N/A' for k in self.accumulated_returns.keys()},
            'Stdev': {k: (self.strategy_metrics.get('annualized_std_dev') if k == 'Strategy' else self.bnh_metrics.get(k.replace(' B&H', ''), {}).get('annualized_std_dev')) for k in self.accumulated_returns.keys()},
            'Mean Pos Return': {k: (self.strategy_metrics.get('mean_positive_return') if k == 'Strategy' else self.bnh_metrics.get(k.replace(' B&H', ''), {}).get('mean_positive_return')) for k in self.accumulated_returns.keys()},
            'Mean Neg Return': {k: (self.strategy_metrics.get('mean_negative_return') if k == 'Strategy' else self.bnh_metrics.get(k.replace(' B&H', ''), {}).get('mean_negative_return')) for k in self.accumulated_returns.keys()},
            'Stdev Pos': {k: (self.strategy_metrics.get('std_dev_positive') if k == 'Strategy' else self.bnh_metrics.get(k.replace(' B&H', ''), {}).get('std_dev_positive')) for k in self.accumulated_returns.keys()},
            'Stdev Neg': {k: (self.strategy_metrics.get('std_dev_negative') if k == 'Strategy' else self.bnh_metrics.get(k.replace(' B&H', ''), {}).get('std_dev_negative')) for k in self.accumulated_returns.keys()},
            'Sharpe Ratio': {k: (self.strategy_metrics.get('sharpe_ratio') if k == 'Strategy' else self.bnh_metrics.get(k.replace(' B&H', ''), {}).get('sharpe_ratio')) for k in self.accumulated_returns.keys()},
            'Sortino Ratio': {k: (self.strategy_metrics.get('sortino_ratio') if k == 'Strategy' else self.bnh_metrics.get(k.replace(' B&H', ''), {}).get('sortino_ratio')) for k in self.accumulated_returns.keys()},
            'Omega Ratio': {k: (self.strategy_metrics.get('omega_ratio') if k == 'Strategy' else self.bnh_metrics.get(k.replace(' B&H', ''), {}).get('omega_ratio')) for k in self.accumulated_returns.keys()},
            'Max Drawdown': {k: (self.strategy_metrics.get('max_drawdown') if k == 'Strategy' else self.bnh_metrics.get(k.replace(' B&H', ''), {}).get('max_drawdown')) for k in self.accumulated_returns.keys()},
        }

        # Format the metrics for display to exactly match the image
        formatted_metrics = {}
        for metric_name, values in metrics_data.items():
            formatted_metrics[metric_name] = {}

            for strategy, value in values.items():
                if value is None:
                    formatted_metrics[metric_name][strategy] = "N/A"
                    continue

                if metric_name == 'Total increase':
                    # Format as percentage with 2 decimal places (e.g., "916.92%")
                    formatted_metrics[metric_name][strategy] = f"{value:.2f}%"
                elif metric_name == 'Num Trades':
                    # Format as integer (e.g., "660")
                    if strategy == 'Strategy':
                        # For strategy, use the actual number of trades
                        formatted_metrics[metric_name][strategy] = f"{int(value)}"
                    else:
                        # For B&H, always use 1
                        formatted_metrics[metric_name][strategy] = "1"
                elif metric_name == 'Time Between Trades':
                    # Format as days (e.g., "1.73 days")
                    if strategy == 'Strategy' and isinstance(value, str):
                        formatted_metrics[metric_name][strategy] = value
                    elif strategy == 'Strategy':
                        formatted_metrics[metric_name][strategy] = f"{value:.2f} days"
                    else:
                        # For B&H, use "N/A"
                        formatted_metrics[metric_name][strategy] = "N/A"
                elif metric_name in ['Stdev', 'Mean Pos Return', 'Mean Neg Return', 'Stdev Pos', 'Stdev Neg']:
                    # Format with 2 decimal places (e.g., "0.71")
                    formatted_metrics[metric_name][strategy] = f"{value:.2f}"
                elif metric_name in ['Sharpe Ratio', 'Sortino Ratio', 'Omega Ratio']:
                    # Format with 2 decimal places (e.g., "0.25")
                    formatted_metrics[metric_name][strategy] = f"{value:.2f}"
                elif metric_name == 'Max Drawdown':
                    # Format with 2 decimal places (e.g., "46.19")
                    formatted_metrics[metric_name][strategy] = f"{value:.2f}"
                else:
                    # Default formatting
                    formatted_metrics[metric_name][strategy] = f"{value:.2f}"

        # Create the table data in the exact order from the image
        table_data = []

        # Make sure we only include strategies that are in the accumulated_returns
        valid_strategies = ['Strategy']
        for asset in self.selected_assets:
            asset_bnh = f"{asset} B&H"
            if asset_bnh in self.accumulated_returns:
                valid_strategies.append(asset_bnh)

        # Create rows for each metric in the specified order
        for metric_name in metrics_order:
            row = []
            for strategy in valid_strategies:
                if strategy in formatted_metrics[metric_name]:
                    row.append(formatted_metrics[metric_name][strategy])
                else:
                    row.append("N/A")
            table_data.append(row)

        # Create a proper table with cells
        ax2.set_title('Performance Metrics', fontsize=12, pad=5)
        ax2.axis('off')  # Hide axes

        # Prepare shortened metric names for better display
        short_metric_names = []
        for metric_name in metrics_order:
            short_name = metric_name
            if metric_name == "Total increase":
                short_name = "Total %"
            elif metric_name == "Time Between Trades":
                short_name = "Trade Interval"
            elif metric_name == "Mean Pos Return":
                short_name = "Mean Pos %"
            elif metric_name == "Mean Neg Return":
                short_name = "Mean Neg %"
            elif metric_name == "Max Drawdown":
                short_name = "Max DD %"
            short_metric_names.append(short_name)

        # Prepare table data with assets as rows and metrics as columns
        table_rows = []
        for strategy in valid_strategies:
            row_data = []
            for metric_name in metrics_order:
                if strategy in formatted_metrics[metric_name]:
                    row_data.append(formatted_metrics[metric_name][strategy])
                else:
                    row_data.append("N/A")
            table_rows.append(row_data)

        # Create the table
        table = ax2.table(
            cellText=table_rows,
            rowLabels=valid_strategies,
            colLabels=short_metric_names,
            loc='center',
            cellLoc='center'
        )

        # Style the table for the side-by-side layout
        table.auto_set_font_size(False)
        table.set_fontsize(9)  # Increased font size for better readability

        # Adjust table scale for the vertical orientation in the side panel
        # Increase both width and height scale for bigger cells
        table.scale(1.2, 1.8)  # Width and height scale increased for larger cells

        # Set cell properties for better visibility
        for (i, j), cell in table.get_celld().items():
            cell.set_text_props(color='white')
            cell.set_facecolor('black')
            cell.set_edgecolor('gray')

            # Make header row and first column bold
            if i == 0 or j == -1:  # Header row or first column
                cell.set_text_props(weight='bold')
                cell.set_facecolor('#333333')  # Slightly lighter background for headers

        # Add execution time information
        # Use total_elapsed_time if available, otherwise fall back to elapsed_time
        display_time = self.total_elapsed_time if self.total_elapsed_time is not None else self.elapsed_time

        if display_time:
            time_text = f"Execution time: {format_elapsed_time(display_time)}"
            # Use plt.figtext instead of fig.text to ensure it's visible
            plt.figtext(0.5, 0.03, time_text, ha='center', fontsize=10, color='white', weight='bold')
        else:
            logging.warning("No elapsed time available for display")

        # Add note about strategy at the bottom of the figure
        if self.n_assets == 1:
            plt.figtext(0.5, 0.01, f"Note: Strategy invests in the best-performing asset at each rebalance point. Colored segments show which asset is held over time.",
                       ha='center', fontsize=10, color='white', alpha=0.8)
        else:
            if self.use_weighted_allocation:
                weights_str = ", ".join([f"{w*100:.1f}%" for w in self.weights])
                plt.figtext(0.5, 0.01, f"Note: Strategy invests in top {self.n_assets} assets with weighted allocation ({weights_str}) at each rebalance point. Continuous blue line shows the strategy equity curve over time.",
                           ha='center', fontsize=10, color='white', alpha=0.8)
            else:
                plt.figtext(0.5, 0.01, f"Note: Strategy invests equally in top {self.n_assets} assets at each rebalance point. Continuous blue line shows the strategy equity curve over time.",
                           ha='center', fontsize=10, color='white', alpha=0.8)

        # Adjust spacing between subplots for horizontal layout
        # No need for hspace adjustment since we're using a horizontal layout
        # wspace was already set earlier

        # Create main output directory if it doesn't exist
        main_output_dir = "realistic_backtest"
        if not os.path.exists(main_output_dir):
            os.makedirs(main_output_dir)

        # Determine which subdirectory to use based on configuration
        if self.n_assets == 1:
            # For single asset (strongest asset) configuration
            sub_dir = "best_asset"
        elif self.use_weighted_allocation:
            # For weighted allocation
            sub_dir = "weighted"
        else:
            # For equal allocation with multiple assets
            sub_dir = "equal_weighted"

        # Create the subdirectory if it doesn't exist
        output_dir = os.path.join(main_output_dir, sub_dir)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logging.warning(f"Created subdirectory: {output_dir}")

        # Get trend method from config
        from src.config_manager import load_config
        config = load_config()
        trend_method = config.get('settings', {}).get('trend_method', 'RSI')

        # Determine if MTPI is being used
        mtpi_used = self.mtpi_signals is not None
        mtpi_status = "with_mtpi" if mtpi_used else "no_mtpi"

        # Determine rebalancing status
        rebalance_status = f"rebal_{int(self.rebalance_threshold*100)}pct" if self.enable_rebalancing else "no_rebal"

        # Create a descriptive filename with all parameters
        if self.use_weighted_allocation:
            # Add weighted allocation info to filename
            if self.weights:
                weights_str = "-".join([f"{w*100:.0f}" for w in self.weights])
            elif self.n_assets == 3:
                weights_str = "70-20-10"  # Default for 3 assets
            else:
                weights_str = "equal"

            filename = (
                f'allocation_test_'
                f'{trend_method.replace(" ", "_")}_'  # Trend indicator type (RSI or PGO For Loop)
                f'{self.timeframe}_'                  # Chart timeframe
                f'{self.mtpi_timeframe}_'             # MTPI timeframe
                f'{mtpi_status}_'                     # Whether MTPI was used
                f'{rebalance_status}_'                # Rebalancing configuration
                f'assets{len(self.selected_assets)}_' # Number of assets used
                f'best{self.n_assets}_'               # Number of best assets selected
                f'weighted_{weights_str}_'            # Weighted allocation info
                f'{self.execution_timing}_'           # Execution timing (candle_close or manual_12pm)
                f'since_{self.analysis_start_date.replace("-", "")}'  # Analysis start date
                f'.png'
            )
        else:
            filename = (
                f'allocation_test_'
                f'{trend_method.replace(" ", "_")}_'  # Trend indicator type (RSI or PGO For Loop)
                f'{self.timeframe}_'                  # Chart timeframe
                f'{self.mtpi_timeframe}_'             # MTPI timeframe
                f'{mtpi_status}_'                     # Whether MTPI was used
                f'{rebalance_status}_'                # Rebalancing configuration
                f'assets{len(self.selected_assets)}_' # Number of assets used
                f'best{self.n_assets}_'               # Number of best assets selected
                f'{self.execution_timing}_'           # Execution timing (candle_close or manual_12pm)
                f'since_{self.analysis_start_date.replace("-", "")}'  # Analysis start date
                f'.png'
            )

        # Full path for the output file
        output_path = os.path.join(output_dir, filename)

        # Save figure with high resolution and proper aspect ratio
        plt.savefig(
            output_path,
            dpi=150,
            bbox_inches='tight',
            facecolor='black'  # Ensure black background is saved
        )

        # Close the figure to prevent memory leaks and multiple figure warnings
        plt.close()

    def align_dataframes(self, data_dict, timeframe='1d'):
        """
        Align DataFrames to a continuous datetime index while tracking each asset's first available date.
        This ensures assets are only included in the strategy after they actually exist.
        """
        if not data_dict:
            return {}

        # First, ensure all dataframes have consistent timezone handling
        for symbol, df in data_dict.items():
            if df.empty:
                continue

            # Check if index is timezone-naive and convert to aware (UTC) if needed
            if df.index.tz is None:
                logging.info(f"Converting timezone-naive index to timezone-aware (UTC) for {symbol}")
                df.index = df.index.tz_localize('UTC')
                data_dict[symbol] = df

        # Find the global min and max dates
        global_min_date = None
        global_max_date = None

        # Track each asset's first available date
        self.asset_first_dates = {}

        for symbol, df in data_dict.items():
            if df.empty:
                continue

            # Store the first date for each asset
            asset_min_date = df.index.min()
            asset_max_date = df.index.max()

            # Special handling for AUTISM token - set first available date to July 19, 2024
            if symbol == 'AUTISM/USDT':
                autism_start_date = pd.Timestamp('2024-07-19', tz='UTC')
                if asset_min_date < autism_start_date:
                    logging.warning(f"Setting AUTISM/USDT first available date to 2024-07-19 instead of {asset_min_date.date()} to avoid skewing strategy results")
                    asset_min_date = autism_start_date

            # Save the first date this asset is available
            # This should match the AllocationReporter behavior to ensure consistent indicator warmup periods
            self.asset_first_dates[symbol] = asset_min_date
            logging.info(f"Asset {symbol} first available date: {asset_min_date}")

            # Update global min date - use the earliest date available in any asset
            # This ensures we include all historical data for charting
            if global_min_date is None or asset_min_date < global_min_date:
                global_min_date = asset_min_date

            if global_max_date is None or asset_max_date > global_max_date:
                global_max_date = asset_max_date

        if global_min_date is None or global_max_date is None:
            logging.warning("No valid dates found in any dataframes.")
            return {}

        # Map timeframe to pandas frequency
        timeframe_to_freq = {
            '1m': 'min', '5m': '5min', '15m': '15min', '30m': '30min',
            '1h': 'H', '2h': '2H', '3h': '3H', '4h': '4H', '6h': '6H', '8h': '8H', '12h': '12H',
            '1d': 'D', '3d': '3D', '1w': 'W', '1M': 'M'
        }

        # Get the frequency
        freq = timeframe_to_freq.get(timeframe, 'D')

        # Create a continuous date range starting from the earliest available date
        # This ensures we include all historical data for charting
        continuous_index = pd.date_range(start=global_min_date, end=global_max_date, freq=freq)

        # Reindex each DataFrame
        aligned_data = {}
        for symbol, df in data_dict.items():
            if df.empty:
                continue

            # Get the first date for this asset
            first_date = self.asset_first_dates[symbol]

            # Create a mask for dates when this asset exists
            exists_mask = continuous_index >= first_date

            # Reindex to the continuous index but only fill values after the asset exists
            reindexed_df = pd.DataFrame(index=continuous_index, columns=df.columns)

            # Only copy data for dates when the asset exists
            valid_dates = continuous_index[exists_mask]
            if not valid_dates.empty:
                # Get the intersection of valid_dates and df.index
                common_dates = df.index.intersection(valid_dates)

                # Copy data for common dates
                reindexed_df.loc[common_dates] = df.loc[common_dates]

                # Forward fill only after the first date
                reindexed_df = reindexed_df.ffill()
                # Ensure proper data types after forward fill
                reindexed_df = reindexed_df.infer_objects(copy=False)

            aligned_data[symbol] = reindexed_df

        return aligned_data

def main():
    """Main function to run the allocation test."""
    # Start timing the entire process
    total_start_time = time.time()

    parser = argparse.ArgumentParser(description='Test allocation logic in the top_n strategy')
    parser.add_argument('--timeframe', type=str, default='4h', help='Timeframe for asset data')
    parser.add_argument('--mtpi-timeframe', type=str, default='1d', help='Timeframe for MTPI signals')
    parser.add_argument('--analysis-start-date', type=str, default='2023-02-15', help='Analysis start date')
    parser.add_argument('--analysis-end-date', type=str, default=None, help='Analysis end date (optional, defaults to current date)')
    parser.add_argument('--n-assets', type=int, default=2, help='Number of top assets to select')
    parser.add_argument('--assets', nargs='+', default=[], help='Assets to include (if not specified, defaults to BTC/USDT, ETH/USDT, SOL/USDT, SUI/USDT unless only GeckoTerminal tokens are used)')
    parser.add_argument('--no-cache', action='store_true', help='Disable cache')
    parser.add_argument('--force-refresh', action='store_true', help='Force refresh cached data')
    parser.add_argument('--no-wait-for-confirmed-signals', action='store_true', help='Disable waiting for confirmed signals')
    parser.add_argument('--trend-method', type=str, choices=['RSI', 'PGO', 'PGO For Loop', 'MTPI'], default=None,
                      help='Trend detection method (RSI, PGO/PGO For Loop, or MTPI). Note: PGO is an alias for "PGO For Loop"')
    parser.add_argument('--no-mtpi', action='store_true', help='Disable MTPI signal filtering')

    # MTPI indicator selection arguments
    parser.add_argument('--mtpi-indicators', nargs='+',
                      choices=['pgo', 'bollinger_bands', 'dwma_score', 'median_score', 'dema_super_score',
                              'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'],
                      help='Select specific MTPI indicators to use (overrides YAML config)')
    parser.add_argument('--mtpi-combination-method', type=str,
                      choices=['consensus', 'majority', 'weighted_consensus'],
                      help='Method to combine multiple MTPI indicators (overrides YAML config)')
    parser.add_argument('--mtpi-long-threshold', type=float,
                      help='Long signal threshold for MTPI aggregation (overrides YAML config)')
    parser.add_argument('--mtpi-short-threshold', type=float,
                      help='Short signal threshold for MTPI aggregation (overrides YAML config)')

    parser.add_argument('--weighted', action='store_true', help='Use weighted allocation instead of equal allocation')
    parser.add_argument('--weights', type=float, nargs='+',
                      help='Weights for top assets (e.g., 0.7 0.2 0.1 for 70%%, 20%%, 10%% allocation). If not provided, uses default weights.')
    parser.add_argument('--enable-rebalancing', action='store_true',
                      help='Enable automatic portfolio rebalancing when weights drift beyond threshold')
    parser.add_argument('--rebalance-threshold', type=float, default=0.05,
                      help='Portfolio drift threshold for rebalancing as a decimal (default: 0.05 for 5%% drift)')
    parser.add_argument('--transaction-fee', type=float, default=0.001,
                      help='Transaction fee rate as a decimal (default: 0.001 for 0.1%%)')
    parser.add_argument('--execution-timing', type=str, default='candle_close',
                      choices=['candle_close', 'manual_12pm'],
                      help='Execution timing: candle_close (automatic at 00:00 UTC) or manual_12pm (manual at 12:00 UTC)')
    parser.add_argument('--ratio-calculation', type=str, choices=['manual_inversion', 'independent'], default='manual_inversion',
                      help='Ratio calculation method: manual_inversion (default, 1-signal) or independent (calculate both directions separately)')
    parser.add_argument('--tie-breaking-strategy', type=str, choices=['incumbent', 'momentum'], default='momentum',
                      help='Tie-breaking strategy when assets have equal scores: incumbent (keep current leader) or momentum (favor dictionary order)')



    # GeckoTerminal token arguments
    parser.add_argument('--geckoterminal-tokens', nargs='+', default=[],
                      help='GeckoTerminal tokens in format "network:token_address:symbol" (e.g., "solana:DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263:BONK/USDT")')

    # Special token flags for convenience
    parser.add_argument('--include-autism', action='store_true',
                      help='Include AUTISM token in the analysis')
    parser.add_argument('--include-fwog', action='store_true',
                      help='Include FWOG token in the analysis')
    parser.add_argument('--include-toshi', action='store_true',
                      help='Include TOSHI token in the analysis')
    parser.add_argument('--include-altura', action='store_true',
                      help='Include ALTURA token in the analysis')
    parser.add_argument('--include-sigma', action='store_true',
                      help='Include SIGMA token in the analysis')
    parser.add_argument('--include-lockin', action='store_true',
                      help='Include LOCKIN token in the analysis')
    parser.add_argument('--include-spx', action='store_true',
                      help='Include SPX token in the analysis')
    parser.add_argument('--include-ski', action='store_true',
                      help='Include SKI token in the analysis')
    parser.add_argument('--include-chillguy', action='store_true',
                      help='Include CHILLGUY token in the analysis')
    parser.add_argument('--include-apu', action='store_true',
                      help='Include APU token in the analysis')
    parser.add_argument('--include-selfie', action='store_true',
                      help='Include SELFIE token in the analysis')
    parser.add_argument('--include-alienbase', action='store_true',
                      help='Include ALIENBASE token in the analysis')
    parser.add_argument('--include-dogeeth', action='store_true',
                      help='Include DOGEETH token in the analysis')
    parser.add_argument('--include-michi', action='store_true',
                      help='Include MICHI token in the analysis')
    parser.add_argument('--include-mini', action='store_true',
                      help='Include MINI token in the analysis')
    parser.add_argument('--include-nub', action='store_true',
                      help='Include NUB token in the analysis')
    parser.add_argument('--include-popcat', action='store_true',
                      help='Include POPCAT token in the analysis')
    parser.add_argument('--include-giko', action='store_true',
                      help='Include GIKO token in the analysis')
    parser.add_argument('--include-coby', action='store_true',
                      help='Include COBY token in the analysis')
    parser.add_argument('--include-giga', action='store_true',
                      help='Include GIGA token in the analysis')
    parser.add_argument('--include-aura', action='store_true',
                      help='Include AURA token in the analysis')
    parser.add_argument('--include-cocoro', action='store_true',
                      help='Include COCORO token in the analysis')
    parser.add_argument('--include-skibidi', action='store_true',
                      help='Include SKIBIDI token in the analysis')
    parser.add_argument('--include-dolan', action='store_true',
                      help='Include DOLAN token in the analysis')
    parser.add_argument('--include-usa', action='store_true',
                      help='Include USA token in the analysis')
    parser.add_argument('--include-epik', action='store_true',
                      help='Include EPIK token in the analysis')

    # Output options
    parser.add_argument('--output-prefix', type=str, default='',
                      help='Prefix to add to output filenames (e.g., "batch_run")')
    parser.add_argument('--no-matrices', action='store_true',
                      help='Disable generation of debug matrices (significantly improves performance)')

    args = parser.parse_args()

    # Check if trend method is changing
    use_cache = not args.no_cache  # Default cache behavior based on args
    force_refresh = args.force_refresh  # Get force refresh setting

    # Update config if trend method is specified
    if args.trend_method:
        from src.config_manager import load_config, save_config
        config = load_config()

        # Get the current trend method from config
        current_trend_method = config.get('settings', {}).get('trend_method', 'RSI')

        if 'settings' not in config:
            config['settings'] = {}

        # Handle "PGO" as an alias for "PGO For Loop"
        trend_method = args.trend_method
        if trend_method == "PGO":
            trend_method = "PGO For Loop"
            logging.warning(f"Converting trend method 'PGO' to 'PGO For Loop' for compatibility")

        # Check if the trend method is changing
        if current_trend_method != trend_method:
            logging.warning(f"Trend method changing from '{current_trend_method}' to '{trend_method}'. Cache will still be used.")
            # We no longer disable the cache when trend method changes since the raw OHLCV data is the same
            # use_cache = False  # This line has been removed to fix the caching issue

        config['settings']['trend_method'] = trend_method
        save_config(config)

        # Log the trend method that will be used
        logging.warning(f"Using trend method '{trend_method}' for strategy execution")



    # Parse GeckoTerminal tokens if provided
    geckoterminal_tokens = []
    if args.geckoterminal_tokens and GECKOTERMINAL_AVAILABLE:
        for token_str in args.geckoterminal_tokens:
            try:
                network, token_address, symbol = token_str.split(':')
                geckoterminal_tokens.append({
                    'network': network,
                    'token_address': token_address,
                    'symbol': symbol
                })
                logging.warning(f"Added GeckoTerminal token: {symbol} on {network}")
            except ValueError:
                logging.error(f"Invalid GeckoTerminal token format: {token_str}. Expected format: 'network:token_address:symbol'")

    # Add special tokens if requested
    if args.include_autism and GECKOTERMINAL_AVAILABLE:
        # AUTISM token details
        autism_token = {
            'network': 'solana',
            'token_address': 'BkVeSP2GsXV3AYoRJBSZTpFE8sXmcuGnRQcFgoWspump',
            'symbol': 'AUTISM/USDT'
        }
        geckoterminal_tokens.append(autism_token)
        logging.warning(f"Added AUTISM token: {autism_token['symbol']} on {autism_token['network']}")

        # Also add AUTISM to the selected assets list to ensure it's included in the strategy
        if 'AUTISM/USDT' not in args.assets:
            args.assets.append('AUTISM/USDT')
            logging.warning(f"Added AUTISM/USDT to selected assets list")

    # Add FWOG token if requested
    if args.include_fwog and GECKOTERMINAL_AVAILABLE:
        # FWOG token details
        fwog_token = {
            'network': 'solana',
            'token_address': 'A8C3xuqscfmyLrte3VmTqrAq8kgMASius9AFNANwpump',
            'symbol': 'FWOG/USDT'
        }
        geckoterminal_tokens.append(fwog_token)
        logging.warning(f"Added FWOG token: {fwog_token['symbol']} on {fwog_token['network']}")

        # Also add FWOG to the selected assets list to ensure it's included in the strategy
        if 'FWOG/USDT' not in args.assets:
            args.assets.append('FWOG/USDT')
            logging.warning(f"Added FWOG/USDT to selected assets list")

    # Add TOSHI token if requested
    if args.include_toshi and GECKOTERMINAL_AVAILABLE:
        # TOSHI token details
        toshi_token = {
            'network': 'base',
            'token_address': '0xac1bd2486aaf3b5c0fc3fd868558b082a531b2b4',
            'symbol': 'TOSHI/USDT'
        }
        geckoterminal_tokens.append(toshi_token)
        logging.warning(f"Added TOSHI token: {toshi_token['symbol']} on {toshi_token['network']}")

        # Also add TOSHI to the selected assets list to ensure it's included in the strategy
        if 'TOSHI/USDT' not in args.assets:
            args.assets.append('TOSHI/USDT')
            logging.warning(f"Added TOSHI/USDT to selected assets list")

    # Add ALTURA token if requested
    if args.include_altura and GECKOTERMINAL_AVAILABLE:
        # ALTURA token details
        altura_token = {
            'network': 'bsc',
            'token_address': '0x8263cd1601fe73c066bf49cc09841f35348e3be0',
            'symbol': 'ALTURA/USDT'
        }
        geckoterminal_tokens.append(altura_token)
        logging.warning(f"Added ALTURA token: {altura_token['symbol']} on {altura_token['network']}")

        # Also add ALTURA to the selected assets list to ensure it's included in the strategy
        if 'ALTURA/USDT' not in args.assets:
            args.assets.append('ALTURA/USDT')
            logging.warning(f"Added ALTURA/USDT to selected assets list")

    # Add SIGMA token if requested
    if args.include_sigma and GECKOTERMINAL_AVAILABLE:
        sigma_token = {
            'network': 'solana',
            'token_address': '5SVG3T9CNQsm2kEwzbRq6hASqh1oGfjqTtLXYUibpump',
            'symbol': 'SIGMA/USDT'
        }
        geckoterminal_tokens.append(sigma_token)
        logging.warning(f"Added SIGMA token: {sigma_token['symbol']} on {sigma_token['network']}")
        if 'SIGMA/USDT' not in args.assets:
            args.assets.append('SIGMA/USDT')
            logging.warning(f"Added SIGMA/USDT to selected assets list")

    # Add LOCKIN token if requested
    if args.include_lockin and GECKOTERMINAL_AVAILABLE:
        lockin_token = {
            'network': 'solana',
            'token_address': '8Ki8DpuWNxu9VsS3kQbarsCWMcFGWkzzA8pUPto9zBd5',
            'symbol': 'LOCKIN/USDT'
        }
        geckoterminal_tokens.append(lockin_token)
        logging.warning(f"Added LOCKIN token: {lockin_token['symbol']} on {lockin_token['network']}")
        if 'LOCKIN/USDT' not in args.assets:
            args.assets.append('LOCKIN/USDT')
            logging.warning(f"Added LOCKIN/USDT to selected assets list")

    # Add SPX token if requested
    if args.include_spx and GECKOTERMINAL_AVAILABLE:
        spx_token = {
            'network': 'solana',
            'token_address': 'J3NKxxXZcnNiMjKw9hYb2K4LUxgwB6t1FtPtQVsv3KFr',
            'symbol': 'SPX/USDT'
        }
        geckoterminal_tokens.append(spx_token)
        logging.warning(f"Added SPX token: {spx_token['symbol']} on {spx_token['network']}")
        if 'SPX/USDT' not in args.assets:
            args.assets.append('SPX/USDT')
            logging.warning(f"Added SPX/USDT to selected assets list")

    # Add SKI token if requested
    if args.include_ski and GECKOTERMINAL_AVAILABLE:
        ski_token = {
            'network': 'base',
            'token_address': '0x768be13e1680b5ebe0024c42c896e3db59ec0149',
            'symbol': 'SKI/USDT'
        }
        geckoterminal_tokens.append(ski_token)
        logging.warning(f"Added SKI token: {ski_token['symbol']} on {ski_token['network']}")
        if 'SKI/USDT' not in args.assets:
            args.assets.append('SKI/USDT')
            logging.warning(f"Added SKI/USDT to selected assets list")

    # Add CHILLGUY token if requested
    if args.include_chillguy and GECKOTERMINAL_AVAILABLE:
        chillguy_token = {
            'network': 'solana',
            'token_address': 'Df6yfrKC8kZE3KNkrHERKzAetSxbrWeniQfyJY4Jpump',
            'symbol': 'CHILLGUY/USDT'
        }
        geckoterminal_tokens.append(chillguy_token)
        logging.warning(f"Added CHILLGUY token: {chillguy_token['symbol']} on {chillguy_token['network']}")
        if 'CHILLGUY/USDT' not in args.assets:
            args.assets.append('CHILLGUY/USDT')
            logging.warning(f"Added CHILLGUY/USDT to selected assets list")

    # Add APU token if requested
    if args.include_apu and GECKOTERMINAL_AVAILABLE:
        apu_token = {
            'network': 'eth',
            'token_address': '******************************************',
            'symbol': 'APU/USDT'
        }
        geckoterminal_tokens.append(apu_token)
        logging.warning(f"Added APU token: {apu_token['symbol']} on {apu_token['network']}")
        if 'APU/USDT' not in args.assets:
            args.assets.append('APU/USDT')
            logging.warning(f"Added APU/USDT to selected assets list")

    # Add SELFIE token if requested
    if args.include_selfie and GECKOTERMINAL_AVAILABLE:
        selfie_token = {
            'network': 'solana',
            'token_address': '9WPTUkh8fKuCnepRWoPYLH3aK9gSjPHFDenBq2X1Czdp',
            'symbol': 'SELFIE/USDT'
        }
        geckoterminal_tokens.append(selfie_token)
        logging.warning(f"Added SELFIE token: {selfie_token['symbol']} on {selfie_token['network']}")
        if 'SELFIE/USDT' not in args.assets:
            args.assets.append('SELFIE/USDT')
            logging.warning(f"Added SELFIE/USDT to selected assets list")

    # Add ALIENBASE token if requested
    if args.include_alienbase and GECKOTERMINAL_AVAILABLE:
        alienbase_token = {
            'network': 'base',
            'token_address': '******************************************',
            'symbol': 'ALIENBASE/USDT'
        }
        geckoterminal_tokens.append(alienbase_token)
        logging.warning(f"Added ALIENBASE token: {alienbase_token['symbol']} on {alienbase_token['network']}")
        if 'ALIENBASE/USDT' not in args.assets:
            args.assets.append('ALIENBASE/USDT')
            logging.warning(f"Added ALIENBASE/USDT to selected assets list")

    # Add DOGEETH token if requested
    if args.include_dogeeth and GECKOTERMINAL_AVAILABLE:
        dogeeth_token = {
            'network': 'eth',
            'token_address': '******************************************',
            'symbol': 'DOGEETH/USDT'
        }
        geckoterminal_tokens.append(dogeeth_token)
        logging.warning(f"Added DOGEETH token: {dogeeth_token['symbol']} on {dogeeth_token['network']}")
        if 'DOGEETH/USDT' not in args.assets:
            args.assets.append('DOGEETH/USDT')
            logging.warning(f"Added DOGEETH/USDT to selected assets list")

    # Add MICHI token if requested
    if args.include_michi and GECKOTERMINAL_AVAILABLE:
        michi_token = {
            'network': 'solana',
            'token_address': '5mbK36SZ7J19An8jFochhQS4of8g6BwUjbeCSxBSoWdp',
            'symbol': 'MICHI/USDT'
        }
        geckoterminal_tokens.append(michi_token)
        logging.warning(f"Added MICHI token: {michi_token['symbol']} on {michi_token['network']}")
        if 'MICHI/USDT' not in args.assets:
            args.assets.append('MICHI/USDT')
            logging.warning(f"Added MICHI/USDT to selected assets list")

    # Add MINI token if requested
    if args.include_mini and GECKOTERMINAL_AVAILABLE:
        mini_token = {
            'network': 'solana',
            'token_address': '2JcXacFwt9mVAwBQ5nZkYwCyXQkRcdsYrDXn6hj22SbP',
            'symbol': 'MINI/USDT'
        }
        geckoterminal_tokens.append(mini_token)
        logging.warning(f"Added MINI token: {mini_token['symbol']} on {mini_token['network']}")
        if 'MINI/USDT' not in args.assets:
            args.assets.append('MINI/USDT')
            logging.warning(f"Added MINI/USDT to selected assets list")

    # Add NUB token if requested
    if args.include_nub and GECKOTERMINAL_AVAILABLE:
        nub_token = {
            'network': 'solana',
            'token_address': 'GtDZKAqvMZMnti46ZewMiXCa4oXF4bZxwQPoKzXPFxZn',
            'symbol': 'NUB/USDT'
        }
        geckoterminal_tokens.append(nub_token)
        logging.warning(f"Added NUB token: {nub_token['symbol']} on {nub_token['network']}")
        if 'NUB/USDT' not in args.assets:
            args.assets.append('NUB/USDT')
            logging.warning(f"Added NUB/USDT to selected assets list")

    # Add POPCAT token if requested
    if args.include_popcat and GECKOTERMINAL_AVAILABLE:
        popcat_token = {
            'network': 'solana',
            'token_address': '7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr',
            'symbol': 'POPCAT/USDT'
        }
        geckoterminal_tokens.append(popcat_token)
        logging.warning(f"Added POPCAT token: {popcat_token['symbol']} on {popcat_token['network']}")
        if 'POPCAT/USDT' not in args.assets:
            args.assets.append('POPCAT/USDT')
            logging.warning(f"Added POPCAT/USDT to selected assets list")

    # Add GIKO token if requested
    if args.include_giko and GECKOTERMINAL_AVAILABLE:
        giko_token = {
            'network': 'solana',
            'token_address': '3WPep4ufaToK1aS5s8BL9inzeUrt4DYaQCiic6ZkkC1U',
            'symbol': 'GIKO/USDT'
        }
        geckoterminal_tokens.append(giko_token)
        logging.warning(f"Added GIKO token: {giko_token['symbol']} on {giko_token['network']}")
        if 'GIKO/USDT' not in args.assets:
            args.assets.append('GIKO/USDT')
            logging.warning(f"Added GIKO/USDT to selected assets list")

    # Add COBY token if requested
    if args.include_coby and GECKOTERMINAL_AVAILABLE:
        coby_token = {
            'network': 'solana',
            'token_address': '8WnQQRbuEZ3CCDbH5MCVioBbw6o75NKANq9WdPhBDsWo',
            'symbol': 'COBY/USDT'
        }
        geckoterminal_tokens.append(coby_token)
        logging.warning(f"Added COBY token: {coby_token['symbol']} on {coby_token['network']}")
        if 'COBY/USDT' not in args.assets:
            args.assets.append('COBY/USDT')
            logging.warning(f"Added COBY/USDT to selected assets list")

    # Add GIGA token if requested
    if args.include_giga and GECKOTERMINAL_AVAILABLE:
        giga_token = {
            'network': 'solana',
            'token_address': '63LfDmNb3MQ8mw9MtZ2To9bEA2M71kZUUGq5tiJxcqj9',
            'symbol': 'GIGA/USDT'
        }
        geckoterminal_tokens.append(giga_token)
        logging.warning(f"Added GIGA token: {giga_token['symbol']} on {giga_token['network']}")
        if 'GIGA/USDT' not in args.assets:
            args.assets.append('GIGA/USDT')
            logging.warning(f"Added GIGA/USDT to selected assets list")

    # Add AURA token if requested
    if args.include_aura and GECKOTERMINAL_AVAILABLE:
        aura_token = {
            'network': 'solana',
            'token_address': 'DtR4D9FtVoTX2569gaL837ZgrB6wNjj6tkmnX9Rdk9B2',
            'symbol': 'AURA/USDT'
        }
        geckoterminal_tokens.append(aura_token)
        logging.warning(f"Added AURA token: {aura_token['symbol']} on {aura_token['network']}")
        if 'AURA/USDT' not in args.assets:
            args.assets.append('AURA/USDT')
            logging.warning(f"Added AURA/USDT to selected assets list")

    # Add COCORO token if requested
    if args.include_cocoro and GECKOTERMINAL_AVAILABLE:
        cocoro_token = {
            'network': 'base',
            'token_address': '0x937a1cfaf0a3d9f5dc4d0927f72ee5e3e5f82a00',
            'symbol': 'COCORO/USDT'
        }
        geckoterminal_tokens.append(cocoro_token)
        logging.warning(f"Added COCORO token: {cocoro_token['symbol']} on {cocoro_token['network']}")
        if 'COCORO/USDT' not in args.assets:
            args.assets.append('COCORO/USDT')
            logging.warning(f"Added COCORO/USDT to selected assets list")

    # Add SKIBIDI token if requested
    if args.include_skibidi and GECKOTERMINAL_AVAILABLE:
        skibidi_token = {
            'network': 'solana',
            'token_address': 'DPaQfq5sFnoqw2Sh9WMmmASFL9LNu6RdtDqwE1tab2tB',
            'symbol': 'SKIBIDI/USDT'
        }
        geckoterminal_tokens.append(skibidi_token)
        logging.warning(f"Added SKIBIDI token: {skibidi_token['symbol']} on {skibidi_token['network']}")
        if 'SKIBIDI/USDT' not in args.assets:
            args.assets.append('SKIBIDI/USDT')
            logging.warning(f"Added SKIBIDI/USDT to selected assets list")

    # Add DOLAN token if requested
    if args.include_dolan and GECKOTERMINAL_AVAILABLE:
        dolan_token = {
            'network': 'solana',
            'token_address': '4YK1njyeCkBuXG6phNtidJWKCbBhB659iwGkUJx98P5Z',
            'symbol': 'DOLAN/USDT'
        }
        geckoterminal_tokens.append(dolan_token)
        logging.warning(f"Added DOLAN token: {dolan_token['symbol']} on {dolan_token['network']}")
        if 'DOLAN/USDT' not in args.assets:
            args.assets.append('DOLAN/USDT')
            logging.warning(f"Added DOLAN/USDT to selected assets list")

    # Add USA token if requested
    if args.include_usa and GECKOTERMINAL_AVAILABLE:
        usa_token = {
            'network': 'solana',
            'token_address': '69kdRLyP5DTRkpHraaSZAQbWmAwzF9guKjZfzMXzcbAs',
            'symbol': 'USA/USDT'
        }
        geckoterminal_tokens.append(usa_token)
        logging.warning(f"Added USA token: {usa_token['symbol']} on {usa_token['network']}")
        if 'USA/USDT' not in args.assets:
            args.assets.append('USA/USDT')
            logging.warning(f"Added USA/USDT to selected assets list")

    # Add EPIK token if requested
    if args.include_epik and GECKOTERMINAL_AVAILABLE:
        epik_token = {
            'network': 'eth',
            'token_address': '******************************************',
            'symbol': 'EPIK/USDT'
        }
        geckoterminal_tokens.append(epik_token)
        logging.warning(f"Added EPIK token: {epik_token['symbol']} on {epik_token['network']}")
        if 'EPIK/USDT' not in args.assets:
            args.assets.append('EPIK/USDT')
            logging.warning(f"Added EPIK/USDT to selected assets list")

    # Handle asset selection logic
    # If no assets were specified and no GeckoTerminal tokens were added, use default assets
    if not args.assets and not geckoterminal_tokens:
        args.assets = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'SUI/USDT']
        logging.warning(f"No assets specified, using default assets: {args.assets}")
    elif not args.assets and geckoterminal_tokens:
        # If only GeckoTerminal tokens were specified, use only those
        args.assets = [token['symbol'] for token in geckoterminal_tokens]
        logging.warning(f"Using only GeckoTerminal tokens as assets: {args.assets}")
    elif args.assets and geckoterminal_tokens:
        # If both regular assets and GeckoTerminal tokens were specified, use both
        gecko_symbols = [token['symbol'] for token in geckoterminal_tokens]
        for symbol in gecko_symbols:
            if symbol not in args.assets:
                args.assets.append(symbol)
        logging.warning(f"Using combination of specified assets and GeckoTerminal tokens: {args.assets}")
    else:
        # If only regular assets were specified, use those
        logging.warning(f"Using specified assets: {args.assets}")

    # Create and run the tester
    tester = AllocationTester(
        timeframe=args.timeframe,
        mtpi_timeframe=args.mtpi_timeframe,
        analysis_start_date=args.analysis_start_date,
        analysis_end_date=args.analysis_end_date,
        n_assets=args.n_assets,
        transaction_fee_rate=args.transaction_fee,
        selected_assets=args.assets,
        use_cache=use_cache,  # Use the determined cache setting
        wait_for_confirmed_signals=not args.no_wait_for_confirmed_signals,
        use_mtpi=not args.no_mtpi,
        use_weighted_allocation=args.weighted,
        weights=args.weights if args.weighted else None,
        enable_rebalancing=args.enable_rebalancing,
        rebalance_threshold=args.rebalance_threshold,
        geckoterminal_tokens=geckoterminal_tokens,
        execution_timing=args.execution_timing,
        ratio_calculation=args.ratio_calculation,
        tie_breaking_strategy=args.tie_breaking_strategy,
        # MTPI indicator selection parameters
        mtpi_indicators=args.mtpi_indicators,
        mtpi_combination_method=args.mtpi_combination_method,
        mtpi_long_threshold=args.mtpi_long_threshold,
        mtpi_short_threshold=args.mtpi_short_threshold
    )

    # Set no_matrices flag in the tester object
    tester.no_matrices = args.no_matrices

    # Set force_refresh in the tester object
    tester.force_refresh = force_refresh

    # Run the test
    tester.fetch_data()
    tester.fetch_mtpi_signals()
    tester.calculate_scores()
    tester.run_strategy_with_detailed_logging()

    # Calculate total elapsed time before analyze_allocation
    total_elapsed_time = time.time() - total_start_time

    # Store the total elapsed time in the tester object for use in plot_results
    tester.total_elapsed_time = total_elapsed_time

    # Now run the analysis and plotting
    tester.analyze_allocation()

    # Update total elapsed time after everything is done
    total_elapsed_time = time.time() - total_start_time

    # Display summary of performance metrics
    if tester.strategy_metrics:
        logging.warning("\n--- Performance Metrics Summary ---")
        logging.warning(f"Total Return: {format_metric(tester.accumulated_returns.get('Strategy'), is_percent=True)}")
        logging.warning(f"Sharpe Ratio: {format_metric(tester.strategy_metrics.get('sharpe_ratio'))}")
        logging.warning(f"Sortino Ratio: {format_metric(tester.strategy_metrics.get('sortino_ratio'))}")
        logging.warning(f"Max Drawdown: {format_metric(tester.strategy_metrics.get('max_drawdown'), is_percent=True)}")
        logging.warning(f"Number of Trades: {tester.strategy_metrics.get('num_trades')}")

    logging.warning("Allocation test completed successfully")

    # Get trend method from config for the final message
    from src.config_manager import load_config
    config = load_config()
    trend_method = config.get('settings', {}).get('trend_method', 'RSI')

    # Determine if MTPI is being used
    mtpi_used = "with_mtpi" if tester.use_mtpi and tester.mtpi_signals is not None else "no_mtpi"

    # Determine rebalancing status
    rebalance_status = f"rebal_{int(args.rebalance_threshold*100)}pct" if args.enable_rebalancing else "no_rebal"

    # Create the same filename format as in plot_results for consistency
    # Add weighted allocation info to filename if used
    if args.weighted:
        if args.weights:
            weights_str = "-".join([f"{w*100:.0f}" for w in args.weights])
        elif args.n_assets == 3:
            weights_str = "70-20-10"  # Default for 3 assets
        else:
            weights_str = "equal"

        # Add output prefix if provided
        prefix = f"{args.output_prefix}_" if args.output_prefix else ""

        filename = (
            f'{prefix}allocation_test_'
            f'{trend_method.replace(" ", "_")}_'
            f'{args.timeframe}_'
            f'{args.mtpi_timeframe}_'
            f'{mtpi_used}_'
            f'{rebalance_status}_'
            f'assets{len(args.assets)}_'
            f'best{args.n_assets}_'
            f'weighted_{weights_str}_'
            f'{args.execution_timing}_'
            f'since_{args.analysis_start_date.replace("-", "")}'
            f'.png'
        )
    else:
        # Add output prefix if provided
        prefix = f"{args.output_prefix}_" if args.output_prefix else ""

        filename = (
            f'{prefix}allocation_test_'
            f'{trend_method.replace(" ", "_")}_'
            f'{args.timeframe}_'
            f'{args.mtpi_timeframe}_'
            f'{mtpi_used}_'
            f'{rebalance_status}_'
            f'assets{len(args.assets)}_'
            f'best{args.n_assets}_'
            f'{args.execution_timing}_'
            f'since_{args.analysis_start_date.replace("-", "")}'
            f'.png'
        )

    # Determine which subdirectory to use based on configuration
    if args.n_assets == 1:
        # For single asset (strongest asset) configuration
        sub_dir = "best_asset"
    elif args.weighted:
        # For weighted allocation
        sub_dir = "weighted"
    else:
        # For equal allocation with multiple assets
        sub_dir = "equal_weighted"

    # Display the path to the saved chart
    output_path = os.path.join("realistic_backtest", sub_dir, filename)
    logging.warning(f"Results saved to: {output_path}")

def run_strategy_for_web(use_mtpi_signal=True, mtpi_indicator_type='PGO', mtpi_timeframe='1d', mtpi_pgo_length=35, mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58, timeframe='1d', analysis_start_date='2023-10-20', analysis_end_date=None, transaction_fee_rate=0.001, selected_assets=None, trading_assets=None, initial_capital=10000, trend_method=None, pgo_length=None, pgo_upper_threshold=None, pgo_lower_threshold=None, use_cache=True, force_refresh_cache=False, cache_dir=None, max_cache_age_days=1, n_assets=1, debug_matrices=False, use_weighted_allocation=False, weights=None, enable_rebalancing=False, rebalance_threshold=0.05, save_snapshot=False, snapshot_dir=None, snapshot_prefix=None, plot_start_date=None, save_metrics_to_csv=False, metrics_dir=None, run_id=None, asset_count=None, geckoterminal_tokens=None, context='backtesting', mtpi_indicators=None, mtpi_combination_method=None, mtpi_long_threshold=None, mtpi_short_threshold=None, execution_timing='candle_close', ratio_calculation='manual_inversion', tie_breaking_strategy='momentum', config_path=None):
    """
    Run the strategy and return the results for web display.
    This function is similar to main() but returns the data instead of just plotting it.

    Args:
        use_mtpi_signal: Whether to use MTPI signal filtering
        mtpi_indicator_type: Type of MTPI indicator ('PGO' or 'RSI')
        mtpi_timeframe: Timeframe for MTPI signals
        mtpi_pgo_length: Length parameter for PGO indicator
        mtpi_upper_threshold: Upper threshold for PGO indicator
        mtpi_lower_threshold: Lower threshold for PGO indicator
        timeframe: Timeframe for asset data
        analysis_start_date: Start date for the analysis
        analysis_end_date: End date for the analysis (optional)
        transaction_fee_rate: Transaction fee rate (e.g., 0.001 for 0.1%)
        selected_assets: List of assets to include in trend detection analysis (USDT pairs)
        trading_assets: List of assets for trading execution (USDC pairs)
        initial_capital: Initial capital for the strategy
        trend_method: Trend detection method ('RSI' or 'PGO')
        pgo_length: Length parameter for PGO indicator in trend detection
        pgo_upper_threshold: Upper threshold for PGO indicator in trend detection
        pgo_lower_threshold: Lower threshold for PGO indicator in trend detection
        use_cache: Whether to use cached data
        force_refresh_cache: Whether to force refresh cached data
        cache_dir: Directory for cached data
        max_cache_age_days: Maximum age of cached data in days
        n_assets: Number of top assets to select
        debug_matrices: Whether to generate debug matrices
        use_weighted_allocation: Whether to use weighted allocation
        weights: Custom weights for allocation
        save_snapshot: Whether to save a snapshot of the strategy results
        snapshot_dir: Directory to save the snapshot to
        snapshot_prefix: Prefix to add to the snapshot filename
        plot_start_date: Start date for plotting
        save_metrics_to_csv: Whether to save performance metrics to CSV (default: False)
        metrics_dir: Directory for storing metrics CSV files (default: "Performance_Metrics")
        run_id: Unique identifier for the current strategy run
        asset_count: Number of assets in the rotation
        geckoterminal_tokens: List of GeckoTerminal tokens to include in the analysis
        context: Execution context ('live_service' for background service, 'backtesting' for testing)
        mtpi_indicators: List of MTPI indicators to use (overrides single indicator approach)
        mtpi_combination_method: Method to combine multiple MTPI indicators ('consensus', 'majority', 'weighted_consensus')
        mtpi_long_threshold: Long signal threshold for MTPI aggregation
        mtpi_short_threshold: Short signal threshold for MTPI aggregation

    Returns:
        Dict containing the strategy results:
        - strategy_equity: Series with strategy equity values
        - buy_hold_curves: Dict of buy-and-hold curves for each asset
        - best_asset_series: Series showing which asset was held on each day
        - mtpi_signals: Series with MTPI signals
        - performance_metrics: Dict with performance metrics
    """
    logging.info(f"=== RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===")
    logging.info(f"Parameters: use_mtpi_signal={use_mtpi_signal}, mtpi_indicator_type={mtpi_indicator_type}")
    logging.info(f"mtpi_timeframe={mtpi_timeframe}, mtpi_pgo_length={mtpi_pgo_length}")
    logging.info(f"mtpi_upper_threshold={mtpi_upper_threshold}, mtpi_lower_threshold={mtpi_lower_threshold}")
    logging.info(f"timeframe={timeframe}, analysis_start_date={analysis_start_date!r}")
    logging.info(f"n_assets={n_assets}, use_weighted_allocation={use_weighted_allocation}")

    # If trend_method is provided, update the config
    if trend_method:
        logging.info(f"Using provided trend method: {trend_method}")
        from src.config_manager import load_config, save_config
        config = load_config(config_path)

        # Handle "PGO" as an alias for "PGO For Loop"
        if trend_method == "PGO":
            trend_method = "PGO For Loop"
            logging.info(f"Converting trend method 'PGO' to 'PGO For Loop' for compatibility")

        if 'settings' not in config:
            config['settings'] = {}

        config['settings']['trend_method'] = trend_method
        save_config(config, config_path)
    else:
        # Get the current trend method from config
        from src.config_manager import load_config
        config = load_config(config_path)
        trend_method = config.get('settings', {}).get('trend_method', 'RSI')
        logging.info(f"Using trend method from config: {trend_method}")

    # Enhanced logging for weighted allocation
    if use_weighted_allocation:
        logging.info(f"Weighted allocation enabled with weights: {weights}")
        if weights:
            logging.info(f"Weights type: {type(weights)}, values: {weights}")
            logging.info(f"Weights sum: {sum(weights)}")

            # Check if weights need normalization
            if sum(weights) != 1.0:
                total = sum(weights)
                normalized_weights = [w / total for w in weights]
                logging.info(f"Weights need normalization. Normalized weights: {normalized_weights}")
            else:
                logging.info("Weights are already normalized (sum to 1.0)")
        else:
            logging.info("No custom weights provided, will use default weights")
            if n_assets == 3:
                logging.info("Will use default 70-20-10 split for 3 assets")
            else:
                logging.info(f"Will use equal weights for {n_assets} assets")

    # Enhanced logging for selected assets
    if selected_assets:
        logging.info(f"Trend detection assets (USDT): {selected_assets}")
        logging.info(f"Number of trend detection assets: {len(selected_assets)}")
        logging.info(f"Selected assets type: {type(selected_assets)}")
    else:
        logging.info("No trend detection assets provided, will use default assets")

    # Enhanced logging for trading assets
    if trading_assets:
        # Detect quote currency from trading assets
        trading_quote = 'USDC'  # Default
        if trading_assets and len(trading_assets) > 0:
            first_trading_asset = trading_assets[0]
            if '/' in first_trading_asset:
                trading_quote = first_trading_asset.split('/')[1]

        logging.info(f"Trading execution assets ({trading_quote}): {trading_assets}")
        logging.info(f"Number of trading assets: {len(trading_assets)}")
        logging.info(f"Trading assets type: {type(trading_assets)}")
    else:
        logging.info("No trading assets provided, will derive from trend detection assets")

    try:
        # Import the progress tracker from api_server
        from src.api_server import strategy_progress

        # Check for cancellation
        def check_for_cancellation():
            """Check if the strategy calculation has been canceled"""
            if strategy_progress.get("canceled", False):
                logging.info("Strategy calculation was canceled")
                return True
            return False

        # 1. Fetch Data (0-10%)
        strategy_progress["progress"] = 2
        strategy_progress["stage"] = "Fetching market data"

        # Check for cancellation
        if check_for_cancellation():
            return {'error': "Strategy calculation was canceled by the user"}

        # Set up GeckoTerminal tokens if any
        if geckoterminal_tokens is None:
            geckoterminal_tokens = []

        # Create and run the tester
        tester = AllocationTester(
            timeframe=timeframe,
            mtpi_timeframe=mtpi_timeframe,
            analysis_start_date=analysis_start_date,
            analysis_end_date=analysis_end_date,
            n_assets=n_assets,
            transaction_fee_rate=transaction_fee_rate,
            selected_assets=selected_assets,
            use_cache=use_cache,
            initial_capital=initial_capital,
            wait_for_confirmed_signals=True,  # Always use confirmed signals for web
            use_mtpi=use_mtpi_signal,
            use_weighted_allocation=use_weighted_allocation,
            weights=weights,
            enable_rebalancing=enable_rebalancing,
            rebalance_threshold=rebalance_threshold,
            geckoterminal_tokens=geckoterminal_tokens,
            trend_method=trend_method,
            pgo_length=pgo_length,
            pgo_upper_threshold=pgo_upper_threshold,
            pgo_lower_threshold=pgo_lower_threshold,
            context=context,  # Pass context for rate limiting
            execution_timing=execution_timing,  # Pass execution timing parameter
            ratio_calculation=ratio_calculation,  # Pass ratio calculation method
            tie_breaking_strategy=tie_breaking_strategy,  # Pass tie-breaking strategy
            # Multi-indicator MTPI parameters
            mtpi_indicators=mtpi_indicators,
            mtpi_combination_method=mtpi_combination_method,
            mtpi_long_threshold=mtpi_long_threshold,
            mtpi_short_threshold=mtpi_short_threshold,
            config_path=config_path  # Pass the config path to ensure correct configuration is used
        )

        # Set force_refresh in the tester object
        tester.force_refresh = force_refresh_cache

        # Set no_matrices flag based on debug_matrices parameter
        tester.no_matrices = not debug_matrices

        # Update progress
        strategy_progress["progress"] = 10
        strategy_progress["stage"] = "Fetching data"

        # Check for cancellation
        if check_for_cancellation():
            return {'error': "Strategy calculation was canceled by the user"}

        # Run the test steps
        tester.fetch_data()

        # Update progress
        strategy_progress["progress"] = 30
        strategy_progress["stage"] = "Fetching MTPI signals"

        # Check for cancellation
        if check_for_cancellation():
            return {'error': "Strategy calculation was canceled by the user"}

        tester.fetch_mtpi_signals()

        # Update progress
        strategy_progress["progress"] = 40
        strategy_progress["stage"] = "Calculating scores"

        # Check for cancellation
        if check_for_cancellation():
            return {'error': "Strategy calculation was canceled by the user"}

        tester.calculate_scores()

        # Update progress
        strategy_progress["progress"] = 50
        strategy_progress["stage"] = "Running strategy simulation"

        # Check for cancellation
        if check_for_cancellation():
            return {'error': "Strategy calculation was canceled by the user"}

        tester.run_strategy_with_detailed_logging()

        # Update progress
        strategy_progress["progress"] = 80
        strategy_progress["stage"] = "Analyzing results"

        # Check for cancellation
        if check_for_cancellation():
            return {'error': "Strategy calculation was canceled by the user"}

        tester.analyze_allocation()

        # Update progress
        strategy_progress["progress"] = 90
        strategy_progress["stage"] = "Preparing response"

        # Check for cancellation
        if check_for_cancellation():
            return {'error': "Strategy calculation was canceled by the user"}

        # Prepare the results in the format expected by the API server
        results = {}

        # Save a snapshot if requested
        if save_snapshot and tester.strategy_equity_curve is not None:
            try:
                # Get trend method from config
                from src.config_manager import load_config
                config = load_config(config_path)
                trend_method = config.get('settings', {}).get('trend_method', 'RSI')

                # Log the trend method being used for the snapshot
                logging.info(f"Using trend method '{trend_method}' for snapshot generation")

                # Close any existing figures to prevent warnings
                plt.close('all')

                # Create figure for the snapshot with increased height to accommodate metrics table
                plt.figure(figsize=(16, 14), dpi=100)

                # Set style to match TradingView dark theme
                plt.style.use('dark_background')

                # Use GridSpec for more control over subplot sizes - 2 rows (main plot and metrics)
                import matplotlib.gridspec as gridspec
                gs = gridspec.GridSpec(2, 1, height_ratios=[2, 2.5])

                # Filter equity curve data if plot_start_date is provided
                if plot_start_date:
                    try:
                        # Convert plot_start_date to pandas Timestamp if it's a string
                        if isinstance(plot_start_date, str):
                            # Create a timezone-aware timestamp to match the equity curve index
                            # First check if the equity curve index has timezone info
                            if not tester.strategy_equity_curve.empty and tester.strategy_equity_curve.index[0].tzinfo is not None:
                                # Get the timezone from the equity curve index
                                tz = tester.strategy_equity_curve.index[0].tzinfo
                                logging.info(f"Creating timezone-aware timestamp with timezone: {tz}")
                                # Create timezone-aware timestamp with the same timezone
                                plot_start_date = pd.Timestamp(plot_start_date, tz=tz)
                            else:
                                # If equity curve has no timezone, use UTC as default
                                logging.info("Creating timezone-aware timestamp with UTC timezone")
                                plot_start_date = pd.Timestamp(plot_start_date, tz='UTC')

                        logging.info(f"Plot start date: {plot_start_date}, type: {type(plot_start_date)}, tzinfo: {plot_start_date.tzinfo}")
                        if not tester.strategy_equity_curve.empty:
                            logging.info(f"Equity curve index sample: {tester.strategy_equity_curve.index[0]}, type: {type(tester.strategy_equity_curve.index[0])}, tzinfo: {tester.strategy_equity_curve.index[0].tzinfo}")

                        # Log the full equity curve date range for debugging
                        if not tester.strategy_equity_curve.empty:
                            logging.info(f"Full equity curve date range: {tester.strategy_equity_curve.index[0]} to {tester.strategy_equity_curve.index[-1]}")

                        # Check if any dates in the equity curve are greater than or equal to plot_start_date
                        matching_dates = [date for date in tester.strategy_equity_curve.index if date >= plot_start_date]
                        logging.info(f"Found {len(matching_dates)} dates >= {plot_start_date}")
                        if matching_dates:
                            logging.info(f"First matching date: {matching_dates[0]}")

                        # Filter the strategy equity curve to start from plot_start_date
                        filtered_equity_curve = tester.strategy_equity_curve[tester.strategy_equity_curve.index >= plot_start_date]

                        if not filtered_equity_curve.empty:
                            logging.info(f"SUCCESS: Filtering plot to start from {plot_start_date.strftime('%Y-%m-%d')} instead of full backtest")
                            logging.info(f"Filtered equity curve has {len(filtered_equity_curve)} points from {filtered_equity_curve.index[0]} to {filtered_equity_curve.index[-1]}")
                            plot_equity_curve = filtered_equity_curve
                        else:
                            logging.warning(f"No data points found after {plot_start_date.strftime('%Y-%m-%d')}, using full equity curve")
                            logging.warning(f"Full equity curve has {len(tester.strategy_equity_curve)} points from {tester.strategy_equity_curve.index[0]} to {tester.strategy_equity_curve.index[-1]}")
                            plot_equity_curve = tester.strategy_equity_curve
                    except Exception as e:
                        logging.error(f"Error filtering equity curve by date: {e}, using full equity curve")
                        logging.error(f"Exception details: {str(e)}", exc_info=True)
                        plot_equity_curve = tester.strategy_equity_curve
                else:
                    # Use the full equity curve if no plot_start_date is provided
                    plot_equity_curve = tester.strategy_equity_curve

                # Plot 1: Strategy equity curve with logarithmic y-axis
                ax1 = plt.subplot(gs[0])

                # Plot strategy equity curve
                ax1.plot(plot_equity_curve.index, plot_equity_curve.values,
                         label='Strategy', linewidth=2, color='#0080ff')

                # Add buy and hold curves
                for asset in tester.selected_assets:
                    if asset in tester.data_dict:
                        # Get the asset's buy and hold curve from the tester
                        if hasattr(tester, 'bnh_metrics') and asset in tester.bnh_metrics:
                            # If we have the buy and hold curve in the metrics, use it
                            asset_equity = tester.bnh_metrics[asset].get('equity_curve')
                            if asset_equity is not None:
                                # Filter buy and hold curve if plot_start_date is provided
                                if plot_start_date and isinstance(plot_equity_curve, pd.Series):
                                    try:
                                        # Filter the asset equity curve to start from the same date as the strategy curve
                                        filtered_asset_equity = asset_equity[asset_equity.index >= plot_equity_curve.index[0]]

                                        if not filtered_asset_equity.empty:
                                            logging.info(f"Filtering {asset} buy and hold curve to start from {plot_equity_curve.index[0]}")
                                            logging.info(f"Filtered {asset} curve has {len(filtered_asset_equity)} points from {filtered_asset_equity.index[0]} to {filtered_asset_equity.index[-1]}")
                                            asset_equity = filtered_asset_equity
                                        else:
                                            logging.warning(f"No data points found for {asset} after {plot_equity_curve.index[0]}, using full curve")
                                    except Exception as e:
                                        logging.error(f"Error filtering {asset} equity curve: {e}, using full curve")
                                        logging.error(f"Exception details: {str(e)}", exc_info=True)

                                # Define asset colors
                                if asset == 'BTC/USDT' or asset == 'BTC/USDC':
                                    color = '#f7931a'  # Bitcoin orange
                                elif asset == 'ETH/USDT' or asset == 'ETH/USDC':
                                    color = '#627eea'  # Ethereum blue
                                elif asset == 'SOL/USDT' or asset == 'SOL/USDC':
                                    color = '#00ffbd'  # Solana green
                                elif asset == 'SUI/USDT' or asset == 'SUI/USDC':
                                    color = '#ff9332'  # SUI orange
                                elif asset == 'XRP/USDT' or asset == 'XRP/USDC':
                                    color = '#0085C0'  # XRP blue
                                elif asset == 'ADA/USDT' or asset == 'ADA/USDC':
                                    color = '#0033AD'  # Cardano blue
                                elif asset == 'AIXBT/USDT' or asset == 'AIXBT/USDC':
                                    color = '#9370DB'  # Medium purple
                                else:
                                    # Generate a deterministic color based on asset name
                                    import hashlib
                                    hash_object = hashlib.md5(asset.encode())
                                    hash_hex = hash_object.hexdigest()
                                    color = '#' + hash_hex[:6]

                                ax1.plot(asset_equity.index, asset_equity.values,
                                         label=f'{asset} B&H', linewidth=1, alpha=0.7, color=color)

                # Add grid with lower alpha for better visibility
                ax1.grid(True, alpha=0.2, linestyle='--')

                # Set logarithmic scale for y-axis
                ax1.set_yscale('log')

                # Get the current top assets from the latest data
                current_top_assets = []
                if tester.assets_held_df is not None and not tester.assets_held_df.empty:
                    # Get the last row with non-zero allocations
                    last_row = tester.assets_held_df.iloc[-1]
                    # Find assets with non-zero allocation
                    current_top_assets = [asset for asset in last_row.index if last_row[asset] > 0]
                    # Sort by allocation value (descending)
                    current_top_assets.sort(key=lambda x: last_row[x], reverse=True)

                # Format the current top assets for display
                current_assets_str = ""
                if current_top_assets:
                    current_assets_str = f" | Current Top Assets: {', '.join(current_top_assets)}"

                # Add labels and title
                ax1.set_title(f'Strategy Performance - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}{current_assets_str}', fontsize=16)
                ax1.set_xlabel('Date', fontsize=12)
                ax1.set_ylabel('Equity', fontsize=12)

                # Format x-axis dates
                ax1.xaxis.set_major_formatter(plt.matplotlib.dates.DateFormatter('%Y-%m'))
                plt.xticks(rotation=45)

                # Determine a reasonable number of columns for legend based on number of assets
                total_legend_items = len(tester.selected_assets) + 1  # assets + strategy
                legend_ncol = 3 if total_legend_items <= 6 else 4

                # Place legend below the chart
                ax1.legend(loc='lower center', framealpha=0.7, fontsize=8,
                          bbox_to_anchor=(0.5, -0.15), ncol=legend_ncol, handlelength=1.2,
                          columnspacing=0.8, borderaxespad=0.8)

                # Plot 2: Performance metrics table
                ax2 = plt.subplot(gs[1])
                ax2.axis('off')  # Hide axes

                # Define the metrics to display
                metrics_order = [
                    'Total increase',
                    'Num Trades',
                    'Trade Interval',
                    'Stdev',
                    'Mean Pos Return',
                    'Mean Neg Return',
                    'Stdev Pos',
                    'Stdev Neg',
                    'Sharpe Ratio',
                    'Sortino Ratio',
                    'Omega Ratio',
                    'Max Drawdown'
                ]

                # Create data dictionary for each metric
                metrics_data = {}

                # Total increase (%)
                metrics_data['Total increase'] = {'Strategy': tester.strategy_metrics.get('total_increase', 0)}
                for asset in tester.selected_assets:
                    if asset in tester.bnh_metrics:
                        metrics_data['Total increase'][f'{asset} B&H'] = tester.bnh_metrics[asset].get('total_increase', 0)

                # Number of trades
                metrics_data['Num Trades'] = {'Strategy': tester.strategy_metrics.get('num_trades', 0)}
                for asset in tester.selected_assets:
                    metrics_data['Num Trades'][f'{asset} B&H'] = 1  # Buy and hold is always 1 trade

                # Trade interval
                metrics_data['Trade Interval'] = {'Strategy': tester.strategy_metrics.get('avg_time_between_trades', 'N/A')}
                for asset in tester.selected_assets:
                    metrics_data['Trade Interval'][f'{asset} B&H'] = 'N/A'  # Not applicable for buy and hold

                # Standard deviation
                metrics_data['Stdev'] = {'Strategy': tester.strategy_metrics.get('annualized_std_dev', 0)}
                for asset in tester.selected_assets:
                    if asset in tester.bnh_metrics:
                        metrics_data['Stdev'][f'{asset} B&H'] = tester.bnh_metrics[asset].get('annualized_std_dev', 0)

                # Mean positive return
                metrics_data['Mean Pos Return'] = {'Strategy': tester.strategy_metrics.get('mean_positive_return', 0)}
                for asset in tester.selected_assets:
                    if asset in tester.bnh_metrics:
                        metrics_data['Mean Pos Return'][f'{asset} B&H'] = tester.bnh_metrics[asset].get('mean_positive_return', 0)

                # Mean negative return
                metrics_data['Mean Neg Return'] = {'Strategy': tester.strategy_metrics.get('mean_negative_return', 0)}
                for asset in tester.selected_assets:
                    if asset in tester.bnh_metrics:
                        metrics_data['Mean Neg Return'][f'{asset} B&H'] = tester.bnh_metrics[asset].get('mean_negative_return', 0)

                # Standard deviation of positive returns
                metrics_data['Stdev Pos'] = {'Strategy': tester.strategy_metrics.get('std_dev_positive', 0)}
                for asset in tester.selected_assets:
                    if asset in tester.bnh_metrics:
                        metrics_data['Stdev Pos'][f'{asset} B&H'] = tester.bnh_metrics[asset].get('std_dev_positive', 0)

                # Standard deviation of negative returns
                metrics_data['Stdev Neg'] = {'Strategy': tester.strategy_metrics.get('std_dev_negative', 0)}
                for asset in tester.selected_assets:
                    if asset in tester.bnh_metrics:
                        metrics_data['Stdev Neg'][f'{asset} B&H'] = tester.bnh_metrics[asset].get('std_dev_negative', 0)

                # Sharpe ratio
                metrics_data['Sharpe Ratio'] = {'Strategy': tester.strategy_metrics.get('sharpe_ratio', 0)}
                for asset in tester.selected_assets:
                    if asset in tester.bnh_metrics:
                        metrics_data['Sharpe Ratio'][f'{asset} B&H'] = tester.bnh_metrics[asset].get('sharpe_ratio', 0)

                # Sortino ratio
                metrics_data['Sortino Ratio'] = {'Strategy': tester.strategy_metrics.get('sortino_ratio', 0)}
                for asset in tester.selected_assets:
                    if asset in tester.bnh_metrics:
                        metrics_data['Sortino Ratio'][f'{asset} B&H'] = tester.bnh_metrics[asset].get('sortino_ratio', 0)

                # Omega ratio
                metrics_data['Omega Ratio'] = {'Strategy': tester.strategy_metrics.get('omega_ratio', 0)}
                for asset in tester.selected_assets:
                    if asset in tester.bnh_metrics:
                        metrics_data['Omega Ratio'][f'{asset} B&H'] = tester.bnh_metrics[asset].get('omega_ratio', 0)

                # Max drawdown
                metrics_data['Max Drawdown'] = {'Strategy': tester.strategy_metrics.get('max_drawdown', 0)}
                for asset in tester.selected_assets:
                    if asset in tester.bnh_metrics:
                        metrics_data['Max Drawdown'][f'{asset} B&H'] = tester.bnh_metrics[asset].get('max_drawdown', 0)

                # Format the metrics for display
                formatted_metrics = {}
                for metric_name, values in metrics_data.items():
                    formatted_metrics[metric_name] = {}
                    for strategy, value in values.items():
                        if value is None:
                            formatted_metrics[metric_name][strategy] = "N/A"
                            continue

                        if metric_name == 'Total increase':
                            formatted_metrics[metric_name][strategy] = f"{value:.2f}%"
                        elif metric_name == 'Num Trades':
                            formatted_metrics[metric_name][strategy] = f"{int(value)}"
                        elif metric_name == 'Trade Interval':
                            if strategy == 'Strategy' and isinstance(value, str):
                                formatted_metrics[metric_name][strategy] = value
                            elif strategy == 'Strategy':
                                formatted_metrics[metric_name][strategy] = f"{value:.2f} days"
                            else:
                                formatted_metrics[metric_name][strategy] = "N/A"
                        elif metric_name in ['Stdev', 'Mean Pos Return', 'Mean Neg Return', 'Stdev Pos', 'Stdev Neg']:
                            formatted_metrics[metric_name][strategy] = f"{value:.2f}"
                        elif metric_name in ['Sharpe Ratio', 'Sortino Ratio', 'Omega Ratio']:
                            formatted_metrics[metric_name][strategy] = f"{value:.2f}"
                        elif metric_name == 'Max Drawdown':
                            formatted_metrics[metric_name][strategy] = f"{value:.2f}"
                        else:
                            formatted_metrics[metric_name][strategy] = f"{value:.2f}"

                # Prepare table data with assets as rows and metrics as columns
                valid_strategies = ['Strategy']
                for asset in tester.selected_assets:
                    valid_strategies.append(f"{asset} B&H")

                # Prepare shortened metric names for better display
                short_metric_names = []
                for metric_name in metrics_order:
                    short_name = metric_name
                    if metric_name == "Total increase":
                        short_name = "Total %"
                    elif metric_name == "Mean Pos Return":
                        short_name = "Mean Pos %"
                    elif metric_name == "Mean Neg Return":
                        short_name = "Mean Neg %"
                    elif metric_name == "Max Drawdown":
                        short_name = "Max DD %"
                    short_metric_names.append(short_name)

                # Create table rows
                table_rows = []
                for strategy in valid_strategies:
                    row_data = []
                    for metric_name in metrics_order:
                        if strategy in formatted_metrics.get(metric_name, {}):
                            row_data.append(formatted_metrics[metric_name][strategy])
                        else:
                            row_data.append("N/A")
                    table_rows.append(row_data)

                # Create the table
                ax2.set_title('Performance Metrics', fontsize=14, pad=10)
                table = ax2.table(
                    cellText=table_rows,
                    rowLabels=valid_strategies,
                    colLabels=short_metric_names,
                    loc='center',
                    cellLoc='center'
                )

                # Style the table
                table.auto_set_font_size(False)
                table.set_fontsize(8)
                table.scale(1, 1.2)

                # Set cell properties for better visibility
                for (i, j), cell in table.get_celld().items():
                    cell.set_text_props(color='white')
                    cell.set_facecolor('black')
                    cell.set_edgecolor('gray')

                    # Make header row and first column bold
                    if i == 0 or j == -1:  # Header row or first column
                        cell.set_text_props(weight='bold')
                        cell.set_facecolor('#333333')  # Slightly lighter background for headers

                # Adjust spacing between subplots
                plt.subplots_adjust(hspace=0.5)

                # Create the snapshot directory if it doesn't exist
                if snapshot_dir and not os.path.exists(snapshot_dir):
                    os.makedirs(snapshot_dir)
                    logging.info(f"Created snapshot directory: {snapshot_dir}")

                # Generate filename using the candle timestamp from snapshot_prefix if provided
                if snapshot_prefix:
                    # Use the candle timestamp as the prefix for consistent naming
                    prefix = f"{snapshot_prefix}_"

                    # Create a descriptive filename
                    mtpi_status = "with_mtpi" if use_mtpi_signal else "no_mtpi"
                    rebalance_status = f"rebal_{int(rebalance_threshold*100)}pct" if enable_rebalancing else "no_rebal"

                    if use_weighted_allocation and weights:
                        weights_str = "-".join([f"{w*100:.0f}" for w in weights])
                        filename = (
                            f'{prefix}allocation_test_'
                            f'{trend_method.replace(" ", "_")}_'
                            f'{timeframe}_'
                            f'{mtpi_timeframe}_'
                            f'{mtpi_status}_'
                            f'{rebalance_status}_'
                            f'assets{len(selected_assets) if selected_assets else 0}_'
                            f'best{n_assets}_'
                            f'weighted_{weights_str}'
                            f'.png'
                        )
                    else:
                        filename = (
                            f'{prefix}allocation_test_'
                            f'{trend_method.replace(" ", "_")}_'
                            f'{timeframe}_'
                            f'{mtpi_timeframe}_'
                            f'{mtpi_status}_'
                            f'{rebalance_status}_'
                            f'assets{len(selected_assets) if selected_assets else 0}_'
                            f'best{n_assets}'
                            f'.png'
                        )

                    logging.info(f"Using candle timestamp '{snapshot_prefix}' for snapshot filename")
                else:
                    # If no prefix provided, use current timestamp (fallback)
                    current_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    prefix = f"{current_timestamp}_"

                    # Create a descriptive filename
                    mtpi_status = "with_mtpi" if use_mtpi_signal else "no_mtpi"
                    rebalance_status = f"rebal_{int(rebalance_threshold*100)}pct" if enable_rebalancing else "no_rebal"

                    if use_weighted_allocation and weights:
                        weights_str = "-".join([f"{w*100:.0f}" for w in weights])
                        filename = (
                            f'{prefix}allocation_test_'
                            f'{trend_method.replace(" ", "_")}_'
                            f'{timeframe}_'
                            f'{mtpi_timeframe}_'
                            f'{mtpi_status}_'
                            f'{rebalance_status}_'
                            f'assets{len(selected_assets) if selected_assets else 0}_'
                            f'best{n_assets}_'
                            f'weighted_{weights_str}'
                            f'.png'
                        )
                    else:
                        filename = (
                            f'{prefix}allocation_test_'
                            f'{trend_method.replace(" ", "_")}_'
                            f'{timeframe}_'
                            f'{mtpi_timeframe}_'
                            f'{mtpi_status}_'
                            f'{rebalance_status}_'
                            f'assets{len(selected_assets) if selected_assets else 0}_'
                            f'best{n_assets}'
                            f'.png'
                        )

                    logging.warning(f"No candle timestamp provided, using current time: {current_timestamp}")

                # Determine the output path
                if snapshot_dir:
                    output_path = os.path.join(snapshot_dir, filename)
                else:
                    # Use the default directory
                    output_dir = "Live_Strat_Progress"
                    if not os.path.exists(output_dir):
                        os.makedirs(output_dir)
                    output_path = os.path.join(output_dir, filename)

                # Save the figure
                plt.savefig(output_path, dpi=150, bbox_inches='tight')
                plt.close()

                logging.info(f"Saved strategy snapshot to: {output_path}")

                # Add the snapshot path to the results
                results['snapshot_path'] = output_path

            except Exception as e:
                logging.error(f"Error saving strategy snapshot: {e}", exc_info=True)

        # Add strategy equity curve
        if tester.strategy_equity_curve is not None:
            results['strategy_equity'] = tester.strategy_equity_curve

        # Add buy and hold curves
        buy_hold_curves = {}
        for asset in tester.selected_assets:
            if asset in tester.data_dict:
                # Get the asset's buy and hold curve from the tester
                if hasattr(tester, 'bnh_metrics') and asset in tester.bnh_metrics:
                    # If we have the buy and hold curve in the metrics, use it
                    asset_equity = tester.bnh_metrics[asset].get('equity_curve')
                    if asset_equity is not None:
                        buy_hold_curves[asset] = asset_equity
                        logging.info(f"Added {asset} buy-and-hold curve with {len(asset_equity)} points")
                    else:
                        logging.warning(f"No equity curve found for {asset} in bnh_metrics")
                else:
                    logging.warning(f"No bnh_metrics found for {asset}")

        results['buy_hold_curves'] = buy_hold_curves

        # Log the buy-and-hold curves for debugging
        logging.info(f"Added {len(buy_hold_curves)} buy-and-hold curves to results")
        for asset, curve in buy_hold_curves.items():
            logging.info(f"  - {asset}: {len(curve)} points from {curve.index[0]} to {curve.index[-1]}")

        # Add best asset series
        if tester.assets_held_df is not None:
            # For multi-asset strategy, we need to create a best_asset_series
            # that shows which assets were held on each day
            best_asset_series = pd.Series(index=tester.assets_held_df.index, dtype=object)

            for date in tester.assets_held_df.index:
                # Get the assets held on this date
                assets_held = tester.assets_held_df.loc[date]

                # Find the assets with non-zero allocation
                held_assets = [asset for asset in assets_held.index if assets_held[asset] > 0]

                if held_assets:
                    if len(held_assets) == 1:
                        # If only one asset is held, use its name
                        best_asset_series[date] = held_assets[0]
                    else:
                        # If multiple assets are held, join their names
                        best_asset_series[date] = ", ".join(held_assets)
                else:
                    # If no assets are held, use empty string
                    best_asset_series[date] = ""

            # Convert USDT assets to trading pairs if trading_assets is provided
            if trading_assets and selected_assets:
                # Determine target quote currency based on exchange or trading assets
                target_quote = 'USDC'  # Default

                # Check if we can detect exchange from trading assets
                if any(asset.endswith('/EUR') for asset in trading_assets):
                    target_quote = 'EUR'
                elif any(asset.endswith('/USDT') for asset in trading_assets):
                    target_quote = 'USDT'
                elif any(asset.endswith('/USDC') for asset in trading_assets):
                    target_quote = 'USDC'

                logging.info(f"Converting trend detection results from USDT to {target_quote} pairs for trading")

                # Create mapping from USDT to target quote currency
                usdt_to_trading_map = {}
                for usdt_asset in selected_assets:
                    if '/USDT' in usdt_asset:
                        base_asset = usdt_asset.replace('/USDT', '')
                        trading_equivalent = f"{base_asset}/{target_quote}"
                        if trading_equivalent in trading_assets:
                            usdt_to_trading_map[usdt_asset] = trading_equivalent

                logging.info(f"Asset conversion mapping: {usdt_to_trading_map}")

                # Convert best_asset_series from USDT to trading pairs
                converted_best_asset_series = best_asset_series.copy()
                for date in converted_best_asset_series.index:
                    current_assets = converted_best_asset_series[date]
                    if current_assets and isinstance(current_assets, str):
                        # Handle both single assets and comma-separated multiple assets
                        if ',' in current_assets:
                            # Multiple assets
                            asset_list = [asset.strip() for asset in current_assets.split(',')]
                            converted_assets = []
                            for asset in asset_list:
                                if asset in usdt_to_trading_map:
                                    converted_assets.append(usdt_to_trading_map[asset])
                                else:
                                    converted_assets.append(asset)  # Keep as-is if no mapping
                            converted_best_asset_series[date] = ', '.join(converted_assets)
                        else:
                            # Single asset
                            if current_assets in usdt_to_trading_map:
                                converted_best_asset_series[date] = usdt_to_trading_map[current_assets]

                results['best_asset_series'] = converted_best_asset_series
                logging.info(f"Successfully converted best asset series from USDT to {target_quote} pairs")
            else:
                results['best_asset_series'] = best_asset_series

        # Add MTPI signals (only if MTPI is enabled)
        if use_mtpi_signal and tester.mtpi_signals is not None:
            results['mtpi_signals'] = tester.mtpi_signals

            # Calculate and add the current MTPI score using the correct timeframe
            try:
                from src.MTPI_signal_handler import fetch_mtpi_signal_and_score_from_config
                current_signal, current_score = fetch_mtpi_signal_and_score_from_config(timeframe=mtpi_timeframe)
                results['mtpi_score'] = current_score
                logging.info(f"Added current MTPI score to results: {current_score:.6f} (using {mtpi_timeframe} timeframe)")
            except Exception as e:
                logging.error(f"Error calculating current MTPI score: {e}")
                # Fallback: use the latest signal value as score (this was the old buggy behavior)
                if not tester.mtpi_signals.empty:
                    results['mtpi_score'] = float(tester.mtpi_signals.iloc[-1])
                else:
                    results['mtpi_score'] = 0.0
        elif not use_mtpi_signal:
            # When MTPI is disabled, set default values
            results['mtpi_signals'] = None
            results['mtpi_score'] = None
            logging.info("MTPI disabled - skipping MTPI signal calculation")

        # Add assets_held_df to results for multi-asset strategies
        if tester.assets_held_df is not None:
            # Convert assets_held_df from USDT to trading quote currency if trading_assets is provided
            if trading_assets and selected_assets:
                # Determine target quote currency
                target_quote = 'USDC'  # Default
                if any(asset.endswith('/EUR') for asset in trading_assets):
                    target_quote = 'EUR'
                elif any(asset.endswith('/USDT') for asset in trading_assets):
                    target_quote = 'USDT'
                elif any(asset.endswith('/USDC') for asset in trading_assets):
                    target_quote = 'USDC'

                # Create mapping from USDT to target quote currency
                usdt_to_trading_map = {}
                for usdt_asset in selected_assets:
                    if '/USDT' in usdt_asset:
                        base_asset = usdt_asset.replace('/USDT', '')
                        trading_equivalent = f"{base_asset}/{target_quote}"
                        if trading_equivalent in trading_assets:
                            usdt_to_trading_map[usdt_asset] = trading_equivalent

                # Convert column names from USDT to target quote currency
                converted_assets_held_df = tester.assets_held_df.copy()
                for usdt_asset, trading_asset in usdt_to_trading_map.items():
                    if usdt_asset in converted_assets_held_df.columns:
                        converted_assets_held_df = converted_assets_held_df.rename(columns={usdt_asset: trading_asset})

                results['assets_held_df'] = converted_assets_held_df
                logging.info(f"Successfully converted assets_held_df from USDT to {target_quote} pairs")
            else:
                results['assets_held_df'] = tester.assets_held_df

        # Add performance metrics
        if tester.strategy_metrics is not None:
            # Get the latest scores from the daily_scores_df
            latest_scores = {}
            if tester.daily_scores_df is not None and not tester.daily_scores_df.empty:
                # Get the last row of scores
                last_scores_row = tester.daily_scores_df.iloc[-1]
                latest_scores = last_scores_row.to_dict()
                logging.info(f"Latest scores extracted (USDT): {latest_scores}")

                # Convert scores from USDT to trading pairs if trading_assets is provided
                if trading_assets and selected_assets:
                    # Determine target quote currency based on exchange or trading assets
                    target_quote = 'USDC'  # Default

                    # Check if we can detect exchange from trading assets
                    if any(asset.endswith('/EUR') for asset in trading_assets):
                        target_quote = 'EUR'
                    elif any(asset.endswith('/USDT') for asset in trading_assets):
                        target_quote = 'USDT'
                    elif any(asset.endswith('/USDC') for asset in trading_assets):
                        target_quote = 'USDC'

                    # Create mapping from USDT to target quote currency
                    usdt_to_trading_map = {}
                    for usdt_asset in selected_assets:
                        if '/USDT' in usdt_asset:
                            base_asset = usdt_asset.replace('/USDT', '')
                            trading_equivalent = f"{base_asset}/{target_quote}"
                            if trading_equivalent in trading_assets:
                                usdt_to_trading_map[usdt_asset] = trading_equivalent

                    # Convert the scores dictionary
                    converted_scores = {}
                    logging.error(f"[DEBUG] CONVERSION - Original USDT scores: {latest_scores}")
                    logging.error(f"[DEBUG] CONVERSION - Original USDT keys order: {list(latest_scores.keys())}")
                    logging.error(f"[DEBUG] CONVERSION - Conversion mapping: {usdt_to_trading_map}")

                    # Check for tied scores in original USDT scores
                    max_usdt_score = max(latest_scores.values()) if latest_scores else 0
                    tied_usdt_assets = [asset for asset, score in latest_scores.items() if score == max_usdt_score]
                    if len(tied_usdt_assets) > 1:
                        logging.error(f"[DEBUG] CONVERSION - TIED USDT ASSETS: {tied_usdt_assets} (score: {max_usdt_score})")

                    for usdt_asset, score in latest_scores.items():
                        if usdt_asset in usdt_to_trading_map:
                            trading_asset = usdt_to_trading_map[usdt_asset]
                            converted_scores[trading_asset] = score
                            logging.error(f"[DEBUG] CONVERSION - {usdt_asset} -> {trading_asset} (score: {score})")
                        else:
                            converted_scores[usdt_asset] = score  # Keep as-is if no mapping
                            logging.error(f"[DEBUG] CONVERSION - {usdt_asset} kept as-is (score: {score})")

                    latest_scores = converted_scores
                    logging.error(f"[DEBUG] CONVERSION - Final EUR scores: {latest_scores}")
                    logging.error(f"[DEBUG] CONVERSION - Final EUR keys order: {list(latest_scores.keys())}")

                    # Check for tied scores in final EUR scores
                    max_eur_score = max(latest_scores.values()) if latest_scores else 0
                    tied_eur_assets = [asset for asset, score in latest_scores.items() if score == max_eur_score]
                    if len(tied_eur_assets) > 1:
                        logging.error(f"[DEBUG] CONVERSION - TIED EUR ASSETS: {tied_eur_assets} (score: {max_eur_score})")
                        logging.error(f"[DEBUG] CONVERSION - First EUR asset (should be selected): {tied_eur_assets[0]}")

                    logging.info(f"Latest scores converted to {target_quote}: {latest_scores}")

            # Create performance metrics dictionary
            performance_metrics = {
                'strategy': tester.strategy_metrics,
                'buy_hold_metrics': tester.bnh_metrics,
                'latest_scores': latest_scores  # Add the latest scores to the performance metrics
            }

            # Add to results
            results['performance_metrics'] = performance_metrics

            # Save metrics to CSV if requested
            if save_metrics_to_csv:
                try:
                    # Generate timestamp for the metrics
                    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

                    # Get strategy name
                    strategy_name = "AssetRotation"
                    if n_assets == 1:
                        strategy_name = "BestAsset"
                    elif use_weighted_allocation:
                        strategy_name = "WeightedAllocation"
                    else:
                        strategy_name = f"EqualAllocation_{n_assets}"

                    # Save metrics to CSV
                    # If plot_start_date is provided, use it as the performance tracking date
                    # This ensures metrics are tracked from the plot start date rather than the full analysis period
                    performance_tracking_date = plot_start_date if plot_start_date else None

                    metrics_file = save_daily_metrics(
                        metrics=performance_metrics,
                        strategy_name=strategy_name,
                        timeframe=timeframe,
                        mtpi_timeframe=mtpi_timeframe,
                        timestamp=timestamp,
                        metrics_dir=metrics_dir,  # Use provided metrics_dir or default
                        weighted=use_weighted_allocation,
                        weights=weights,
                        analysis_start_date=analysis_start_date,
                        append=True,  # Append to existing file if it exists
                        performance_tracking_date=performance_tracking_date,  # Use plot_start_date for performance tracking
                        run_id=run_id,  # Pass the run ID to create a new file for each run
                        asset_count=asset_count  # Pass the asset count for the filename
                    )

                    logging.info(f"Saved performance metrics to CSV: {metrics_file}")

                    # Add metrics file path to results
                    results['metrics_file'] = metrics_file
                except Exception as e:
                    logging.error(f"Error saving metrics to CSV: {e}", exc_info=True)
                    logging.error(f"Will continue without saving metrics to CSV")

        # Add metadata about MTPI filtering
        results['mtpi_filtering_metadata'] = {
            'mtpiFilteringApplied': tester.use_mtpi,
            'mtpiFilteringHandledBy': 'backend'
        }

        # Update progress
        strategy_progress["progress"] = 100
        strategy_progress["stage"] = "Strategy calculation completed"
        strategy_progress["is_running"] = False

        # Note: We're not creating a second snapshot here anymore.
        # The first snapshot created earlier is sufficient.

        # Add success flag to results
        results['success'] = True

        # Add a message to indicate successful completion
        results['message'] = "Strategy calculation completed successfully"

        # Log the structure of the results for debugging
        logging.info(f"=== RESULTS STRUCTURE DEBUGGING ===")
        logging.info(f"Results type: {type(results)}")
        logging.info(f"Results keys: {results.keys() if isinstance(results, dict) else 'Not a dictionary'}")
        logging.info(f"Success flag set to: {results.get('success', 'Not set')}")
        logging.info(f"Message set to: {results.get('message', 'Not set')}")

        # Log the size of each component
        for key, value in results.items():
            if isinstance(value, pd.Series) or isinstance(value, pd.DataFrame):
                logging.info(f"  - {key}: {type(value)} with {len(value)} entries")
            elif isinstance(value, dict):
                logging.info(f"  - {key}: dict with {len(value)} entries")
            else:
                logging.info(f"  - {key}: {type(value)}")

        # Final verification that success flag is set
        if 'success' not in results:
            logging.error("SUCCESS FLAG NOT SET IN RESULTS! Adding it now.")
            results['success'] = True

        return results

    except Exception as e:
        logging.error(f"Error running strategy for web: {e}", exc_info=True)

        # Update progress tracker on error
        if 'strategy_progress' in locals():
            strategy_progress["progress"] = 100
            strategy_progress["stage"] = f"Error: {str(e)}"
            strategy_progress["is_running"] = False

        # Return error with success=False flag and a clear error message
        error_response = {
            'success': False,
            'error': str(e),
            'message': f"Error running strategy: {str(e)}"
        }

        # Log the error response structure
        logging.error(f"Returning error response with structure: {error_response.keys()}")
        logging.error(f"Error response success flag: {error_response.get('success')}")

        return error_response

if __name__ == "__main__":
    main()


